import fs from 'fs'
import Handlebars from 'handlebars'
import path from 'path'
import type { Payload } from 'payload'
import { SalesService, type RecipientData } from './sales.service'

Handlebars.registerHelper('formatCurrency', (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(value)
})

export class ReportGenerator {
  private salesService: SalesService
  private emailTemplate: Handlebars.TemplateDelegate

  constructor(private payload: Payload) {
    this.salesService = new SalesService(payload)

    const templatePath = path.resolve(__dirname, './templates/sales-report.hbs')
    const templateSource = fs.readFileSync(templatePath, 'utf8')
    this.emailTemplate = Handlebars.compile(templateSource)
  }

  private async generateHtml(recipient: RecipientData): Promise<string | null> {
    if (recipient.events.length === 0) {
      return null
    }

    const templateData = {
      reportDate: new Date().toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),
      events: recipient.events,
    }

    return this.emailTemplate(templateData)
  }

  public async sendReportToRecipient(
    recipientId: number,
    recipientType: 'agent' | 'manager',
    daysAhead?: number,
  ) {
    const recipients = await this.salesService.getRecipientsWithReporting()
    const recipient = recipients.find(
      (r) => r.recipientId === recipientId && r.recipientType === recipientType,
    )

    if (!recipient) {
      this.payload.logger.warn(
        `Recipient not found or has reporting disabled: ${recipientType} ${recipientId}`,
      )
      return
    }

    recipient.events = await this.salesService.getEventsForRecipient(
      recipientId,
      recipientType,
      daysAhead,
    )

    const html = await this.generateHtml(recipient)

    if (html) {
      await this.payload.sendEmail({
        to: recipient.recipientEmail,
        from: process.env.PAYLOAD_NODEMAILER_SENDER as string,
        subject: `Gray Area Sales Report for ${recipient.recipientName}`,
        html,
      })
      this.payload.logger.info(`Sales report sent to ${recipient.recipientEmail}`)
    } else {
      this.payload.logger.info(
        `No events with sales data for ${recipient.recipientEmail}. No report sent.`,
      )
    }
  }
}
