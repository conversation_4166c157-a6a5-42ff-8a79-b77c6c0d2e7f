import type { Payload } from 'payload'

export interface TicketSalesData {
  ticketTypeName: string
  ticketsSold: number
  faceValue: number
  totalGrossValue: number
}

export interface EventSalesData {
  eventId: number
  eventName: string
  eventDate: string
  artistName: string
  venueName: string
  ticketSales: TicketSalesData[]
  totalTicketsSold: number
  totalGrossRevenue: number
}

export interface RecipientData {
  recipientId: number
  recipientName: string
  recipientEmail: string
  recipientType: 'agent' | 'manager'
  events: EventSalesData[]
}

export class SalesDataService {
  constructor(private payload: Payload) {}

  async getActiveEvents(daysAhead?: number) {
    const now = new Date()
    const futureDate = daysAhead ? new Date(now.getTime() + daysAhead * 24 * 60 * 60 * 1000) : null

    const whereClause: any = {
      and: [
        { startDate: { gte: now.toISOString() } },
        { saleOnDate: { lte: now.toISOString() } },
        {
          or: [{ saleOffDate: { exists: false } }, { saleOffDate: { gte: now.toISOString() } }],
        },
      ],
    }

    if (futureDate) {
      whereClause.and.push({ startDate: { lte: futureDate.toISOString() } })
    }

    const { docs: events } = await this.payload.find({
      collection: 'events',
      where: whereClause,
      depth: 2,
      limit: 1000,
    })

    return events
  }

  async getEventSalesData(eventId: number): Promise<EventSalesData | null> {
    try {
      const event = await this.payload.findByID({
        collection: 'events',
        id: eventId,
        depth: 2,
      })

      if (!event) return null

      const { docs: ticketTypes } = await this.payload.find({
        collection: 'ticketTypes',
        where: {
          and: [
            { archived: { not_equals: true } },
            { internalPresentationOnly: { not_equals: true } },
          ],
        },
        depth: 1,
        limit: 1000,
      })

      const ticketSalesData: TicketSalesData[] = []
      let totalTicketsSold = 0
      let totalGrossRevenue = 0

      for (const ticketType of ticketTypes) {
        const { docs: tickets } = await this.payload.find({
          collection: 'tickets',
          where: { ticketType: { equals: ticketType.id } },
          limit: 10000,
        })

        if (tickets.length > 0) {
          const ticketsSold = tickets.length
          const faceValue = (ticketType as any).faceValue || 0
          const totalGrossValue = ticketsSold * faceValue

          ticketSalesData.push({
            ticketTypeName: (ticketType as any).ticketTypeName,
            ticketsSold,
            faceValue,
            totalGrossValue,
          })

          totalTicketsSold += ticketsSold
          totalGrossRevenue += totalGrossValue
        }
      }

      const headliningArtist = (event as any).lineup?.[0]?.artist
      const artistName = headliningArtist?.name || 'Unknown Artist'
      const venueName =
        typeof (event as any).Location?.venue === 'object'
          ? (event as any).Location.venue.name
          : 'Unknown Venue'

      return {
        eventId: event.id,
        eventName: (event as any).name,
        eventDate: new Date((event as any).startDate).toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        }),
        artistName,
        venueName,
        ticketSales: ticketSalesData,
        totalTicketsSold,
        totalGrossRevenue,
      }
    } catch (error) {
      console.error('Error getting event sales data:', error)
      return null
    }
  }

  async getRecipientsWithReporting(): Promise<RecipientData[]> {
    const recipients: RecipientData[] = []

    const { docs: agents } = await this.payload.find({
      collection: 'agents',
      where: { automatedReportingEnabled: { equals: true } },
      depth: 1,
      limit: 1000,
    })

    const { docs: managers } = await this.payload.find({
      collection: 'managers',
      where: { automatedReportingEnabled: { equals: true } },
      depth: 1,
      limit: 1000,
    })

    for (const agent of agents) {
      recipients.push({
        recipientId: agent.id,
        recipientName: (agent as any).name,
        recipientEmail: (agent as any).generalContacInfo.email,
        recipientType: 'agent',
        events: [],
      })
    }

    for (const manager of managers) {
      recipients.push({
        recipientId: manager.id,
        recipientName: (manager as any).name,
        recipientEmail: (manager as any).generalContacInfo.email,
        recipientType: 'manager',
        events: [],
      })
    }

    return recipients
  }

  async getEventsForRecipient(
    recipientId: number,
    recipientType: 'agent' | 'manager',
    daysAhead?: number,
  ): Promise<EventSalesData[]> {
    const activeEvents = await this.getActiveEvents(daysAhead)
    const recipientEvents: EventSalesData[] = []

    for (const event of activeEvents) {
      if ((event as any).lineup) {
        for (const lineupItem of (event as any).lineup) {
          const artist = lineupItem.artist
          if (artist?.representation) {
            const isRepresented = artist.representation.some((rep: any) => {
              if (recipientType === 'agent') {
                return rep.agent && typeof rep.agent === 'object' && rep.agent.id === recipientId
              } else {
                return (
                  rep.manager && typeof rep.manager === 'object' && rep.manager.id === recipientId
                )
              }
            })

            if (isRepresented) {
              const salesData = await this.getEventSalesData(event.id)
              if (salesData) {
                recipientEvents.push(salesData)
              }
              break
            }
          }
        }
      }
    }

    return recipientEvents
  }
}
