import type { Payload } from 'payload'
import type { SendSalesReportInput } from '@/jobs/tasks/sendSalesReport'

export class ReportScheduler {
  constructor(private payload: Payload) {}

  async scheduleWeeklyReports() {
    try {
      const job = await this.payload.jobs.queue({
        task: 'sendSalesReport',
        input: {
          reportType: 'weekly',
        } as SendSalesReportInput,
        queue: 'weekly-reports',
      })

      this.payload.logger.info(`Weekly reports job queued: ${job.id}`)
      return job
    } catch (error) {
      this.payload.logger.error('Error scheduling weekly reports:', error)
      throw error
    }
  }

  async scheduleDailyReports() {
    try {
      const job = await this.payload.jobs.queue({
        task: 'sendSalesReport',
        input: {
          reportType: 'daily',
          daysAhead: 7,
        } as SendSalesReportInput,
        queue: 'daily-reports',
      })

      this.payload.logger.info(`Daily reports job queued: ${job.id}`)
      return job
    } catch (error) {
      this.payload.logger.error('Error scheduling daily reports:', error)
      throw error
    }
  }

  async scheduleReportForRecipient(
    recipientId: number,
    recipientType: 'agent' | 'manager',
    reportType: 'weekly' | 'daily' = 'weekly',
  ) {
    try {
      const job = await this.payload.jobs.queue({
        task: 'sendSalesReport',
        input: {
          reportType,
          recipientId,
          recipientType,
          daysAhead: reportType === 'daily' ? 7 : undefined,
        } as SendSalesReportInput,
        queue: 'manual-reports',
      })

      this.payload.logger.info(
        `Manual report job queued for ${recipientType} ${recipientId}: ${job.id}`,
      )
      return job
    } catch (error) {
      this.payload.logger.error(
        `Error scheduling manual report for ${recipientType} ${recipientId}:`,
        error,
      )
      throw error
    }
  }

  async getJobStatus(jobId: string) {
    try {
      const job = await this.payload.findByID({
        collection: 'payload-jobs',
        id: jobId,
      })
      return job
    } catch (error) {
      this.payload.logger.error(`Error getting job status for ${jobId}:`, error)
      throw error
    }
  }

  async getQueueStatus(queue: string = 'default') {
    try {
      const { docs: jobs } = await this.payload.find({
        collection: 'payload-jobs',
        where: {
          queue: { equals: queue },
          processing: { equals: false },
        },
        limit: 100,
        sort: 'createdAt',
      })

      return {
        queue,
        pendingJobs: jobs.length,
        jobs: jobs.map((job) => ({
          id: job.id,
          task: (job as any).taskSlug,
          createdAt: job.createdAt,
          waitUntil: (job as any).waitUntil,
        })),
      }
    } catch (error) {
      this.payload.logger.error(`Error getting queue status for ${queue}:`, error)
      throw error
    }
  }
}
