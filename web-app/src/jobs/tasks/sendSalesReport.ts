import type { <PERSON><PERSON><PERSON><PERSON> } from 'payload'
import { ReportGenerator } from '@/lib/reporting/report-generator'
import { SalesService } from '@/lib/reporting/sales.service'

export interface SendSalesReportInput {
  reportType: 'weekly' | 'daily'
  recipientId?: number
  recipientType?: 'agent' | 'manager'
  daysAhead?: number
}

export const sendSalesReportTask: TaskHandler<'sendSalesReport'> = async ({
  input,
  req,
}): Promise<any> => {
  const payload = req.payload
  const taskInput = input as SendSalesReportInput
  const { reportType, recipientId, recipientType, daysAhead } = taskInput

  try {
    const salesService = new SalesService(payload)
    const reportGenerator = new ReportGenerator(payload)

    if (recipientId && recipientType) {
      await reportGenerator.sendReportToRecipient(recipientId, recipientType, daysAhead)

      payload.logger.info(`${reportType} sales report sent to ${recipientType} ${recipientId}`)

      return {
        success: true,
        message: `Report sent to ${recipientType} ${recipientId}`,
        sentCount: 1,
        errorCount: 0,
      }
    } else {
      const recipients = await salesService.getRecipientsWithReporting()
      let sentCount = 0
      let errorCount = 0

      for (const recipient of recipients) {
        try {
          const events = await salesService.getEventsForRecipient(
            recipient.recipientId,
            recipient.recipientType,
            daysAhead,
          )

          if (events.length > 0) {
            recipient.events = events
            const html = await reportGenerator.generateHtml(recipient)

            if (html) {
              await payload.sendEmail({
                to: recipient.recipientEmail,
                subject: `Gray Area ${reportType === 'weekly' ? 'Weekly' : 'Daily'} Sales Report for ${recipient.recipientName}`,
                html,
              })
              sentCount++
              payload.logger.info(`${reportType} report sent to ${recipient.recipientEmail}`)
            }
          } else {
            payload.logger.info(`No events for ${recipient.recipientEmail}, skipping report`)
          }
        } catch (error) {
          errorCount++
          payload.logger.error(`Failed to send report to ${recipient.recipientEmail}:`, error)
        }
      }

      payload.logger.info(
        `${reportType} reports completed: ${sentCount} sent, ${errorCount} errors`,
      )

      return {
        success: true,
        message: `${reportType} reports sent to ${sentCount} recipients, ${errorCount} errors`,
        sentCount,
        errorCount,
      }
    }
  } catch (error) {
    payload.logger.error(`Error in ${reportType} sales report task:`, error)
    throw error
  }
}

export default sendSalesReportTask
