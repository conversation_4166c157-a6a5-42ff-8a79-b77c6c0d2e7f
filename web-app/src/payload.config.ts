// storage-adapter-import-placeholder
import { postgresAdapter } from '@payloadcms/db-postgres'
import { s3Storage } from '@payloadcms/storage-s3'
import { multiTenantPlugin } from '@payloadcms/plugin-multi-tenant'
import { migrations } from './migrations'

import sharp from 'sharp' // sharp-import
import path from 'path'
import { BasePayload, buildConfig } from 'payload'
import { fileURLToPath } from 'url'

import { Media } from './collections/Media'
import { Pages } from './collections/Pages'
import { Users } from './collections/Users'
import { plugins } from './plugins'
import { defaultLexical } from '@/fields/defaultLexical'
import { getServerSideURL } from './utilities/getURL'
import { Artists } from './collections/Artists'
import { Countries } from './collections/Countries'
import { Authors } from './collections/Authors'
import { Events } from './collections/Events'
import { Articles } from './collections/Articles'
import { EventBrands } from './collections/EventBrands'
import { Festivals } from './collections/Festivals'
import { Genres } from './collections/Genres'
import { OchoEpisodes } from './collections/OchoEpisodes'
import { Residencies } from './collections/Residencies'
import { Venues } from './collections/Venues'
import { Hubs } from './collections/Hubs'
import { FanUsers } from './collections/FanUsers'
import { Tickets } from './collections/Tickets'
import { Orders } from './collections/Orders'
import { EventOrganizers } from './collections/EventOrganizers'
import { ArtistDeals } from './collections/ArtistDeals'
import { Documents } from './collections/Documents'
import { TicketTypes } from './collections/TicketTypes'

import { seedSanityData } from './lib/sanity/seed-data'
import { Agencies } from './collections/Agencies'
import { Agents } from './collections/Agents'
import { ManagmentCompanies } from './collections/ManagmentCompanies'
import { Managers } from './collections/Managers'
import { FestivalProfiles } from './collections/FestivalProfile'
import { Config } from './payload-types'
import { isSuperAdmin } from './access/isSuperAdmin'
import { getUserTenantIDs } from './utilities/getUserTenantIDs'
import { WorkflowOrchestrator } from './lib/workflow-orchestrator/service'
import { FanNotificationListners } from './collections/FanNotificationListners'
import { startWorkflowOrchestrator } from './app/(payload)/actions/sync/sync-partner-dice-events'
import { Promotions } from './collections/Promotions'
import { nodemailerAdapter } from '@payloadcms/email-nodemailer'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)
const collections = [
  Authors,
  Pages,
  Media,
  FanUsers,
  Users,
  Artists,
  Countries,
  Events,
  Articles,
  EventBrands,
  FestivalProfiles,
  Festivals,
  Genres,
  OchoEpisodes,
  Residencies,
  Venues,
  Hubs,
  Tickets,
  Orders,
  TicketTypes,
  EventOrganizers,
  ArtistDeals,
  Documents,
  Agencies,
  Agents,
  ManagmentCompanies,
  Managers,
  FanNotificationListners,
  Promotions,
].sort((a, b) => a.slug.localeCompare(b.slug))

export default buildConfig({
  onInit: async (payload: BasePayload) => {
    console.log('Payload CMS is initializing...')

    const { seedCountries } = await import('./seeds/seedCountries')

    await seedCountries(payload)

    if (process.env.NODE_ENV === 'production') {
      // remove all queued jobs
      await payload.db.deleteMany({
        collection: 'payload-jobs',
        where: {},
      })

      const orchestrator = new WorkflowOrchestrator(payload, { continueOnError: true })

      orchestrator.addStage([
        { slug: 'crawlSanity' },
        // { slug: 'crawlRAWorkflow' },
        // { slug: 'crawlDicekWorkflow' },
        // { slug: 'crawlSongkickWorkflow' },
        { slug: 'syncDicePartnerEventsWorkflow', needs: ['importSanityDataWorkflow'] },
        { slug: 'syncPlanetscaleUsersWorkflow', needs: ['importSanityDataWorkflow'] },
        {
          slug: 'syncPlanetscalePresaleRegistrationsWorkflow',
          needs: ['syncPlanetscaleUsersWorkflow'],
        },
        { slug: 'importSanityDataWorkflow', needs: ['crawlSanity'] },
        // { slug: 'syncDiceWorkflow', needs: ['crawlDicekWorkflow', 'importSanityDataWorkflow'] },
        // {
        //   slug: 'syncSongkickWorkflow',
        //   needs: ['crawlSongkickWorkflow', 'importSanityDataWorkflow'],
        // },
        {
          slug: 'syncDiceOrdersWorkflow',
          needs: ['syncDicePartnerEventsWorkflow'],
        },
        { slug: 'syncQFlowWorkflow', needs: ['syncDiceOrdersWorkflow'] },
      ])

      orchestrator
        .run()
        .then((jobIdsMap) => {
          console.log('All stages passed, JOB ID of each workflow:', jobIdsMap)
        })
        .catch((err) => {
          console.error('The Pipeline is not complete, some stage has fallen:', err)
        })
    }
  },
  endpoints: [
    {
      path: '/sanity/seed',
      method: 'get',
      handler: seedSanityData,
    },
    {
      path: '/start-orchestrator',
      method: 'get',
      handler: startWorkflowOrchestrator,
    },
  ],
  admin: {
    components: {},
    importMap: {
      baseDir: path.resolve(dirname),
    },
    user: Users.slug,
    livePreview: {
      breakpoints: [
        {
          label: 'Mobile',
          name: 'mobile',
          width: 375,
          height: 667,
        },
        {
          label: 'Tablet',
          name: 'tablet',
          width: 768,
          height: 1024,
        },
        {
          label: 'Desktop',
          name: 'desktop',
          width: 1440,
          height: 900,
        },
      ],
    },
  },
  editor: defaultLexical,
  db: postgresAdapter({
    prodMigrations: migrations,
    pool: {
      connectionString: process.env.DATABASE_URI || '',
    },
  }),
  collections: [...collections],
  cors: [getServerSideURL()].filter(Boolean),
  globals: [],
  plugins: [
    ...plugins,
    s3Storage({
      collections: {
        media: {
          prefix: 'media',
        },
      },
      bucket: process.env.S3_BUCKET || '',
      config: {
        forcePathStyle: true,
        credentials: {
          accessKeyId: process.env.S3_ACCESS_KEY_ID || '',
          secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || '',
        },
        region: process.env.S3_REGION || 'auto',
        endpoint: process.env.S3_ENDPOINT || '',
      },
    }),
    multiTenantPlugin<Config>({
      tenantsSlug: 'eventOrganizers',
      collections: {},
      tenantField: {
        access: {
          read: () => true,
          update: ({ req }) => {
            if (isSuperAdmin(req.user)) {
              return true
            }
            return getUserTenantIDs(req.user).length > 0
          },
        },
      },
      tenantsArrayField: {
        includeDefaultField: false,
      },
      userHasAccessToAllTenants: (user) => isSuperAdmin(user),
    }),
  ],
  secret: process.env.PAYLOAD_SECRET as string,
  sharp,
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  email: nodemailerAdapter({
    defaultFromAddress: process.env.EMAIL_FROM || '',
    defaultFromName: process.env.EMAIL_FROM_NAME || '',
  }),
  jobs: {
    tasks: [
      {
        slug: 'sendSalesReport',
        handler: async (args) => {
          const { sendSalesReportTask } = await import('@/jobs/tasks/sendSalesReport')
          return sendSalesReportTask(args)
        },
      },
    ],
    autoRun: [
      {
        cron: '0 9 * * 1', // Every Monday at 9 AM (weekly reports)
        queue: 'weekly-reports',
        limit: 100,
      },
      {
        cron: '0 9 * * *', // Every day at 9 AM (daily reports for shows within 7 days)
        queue: 'daily-reports',
        limit: 100,
      },
    ],
    shouldAutoRun: async () => {
      return process.env.NODE_ENV === 'production' || process.env.ENABLE_JOBS === 'true'
    },
    jobsCollectionOverrides: ({ defaultJobsCollection }) => {
      if (!defaultJobsCollection.admin) {
        defaultJobsCollection.admin = {}
      }
      defaultJobsCollection.admin.hidden = false
      return defaultJobsCollection
    },
  },
  hooks: {
    afterError: [
      async ({ error }) => {
        console.error('Error in Payload CMS:', error)
      },
    ],
  },
  graphQL: { disable: true },
})
