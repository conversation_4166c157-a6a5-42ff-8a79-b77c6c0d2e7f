/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    agencies: Agency;
    agents: Agent;
    articles: Article;
    artistDeals: ArtistDeal;
    artists: Artist;
    authors: Author;
    countries: Country;
    documents: Document;
    eventBrands: EventBrand;
    eventOrganizers: EventOrganizer;
    events: Event;
    fanNotificationListners: FanNotificationListner;
    fanUsers: FanUser;
    festivalProfiles: FestivalProfile;
    festivals: Festival;
    genres: Genre;
    hubs: Hub;
    managers: Manager;
    managmentCompanies: ManagmentCompany;
    media: Media;
    ochoEpisodes: OchoEpisode;
    orders: Order;
    pages: Page;
    promotions: Promotion;
    residencies: Residency;
    tickets: Ticket;
    ticketTypes: TicketType;
    users: User;
    venues: Venue;
    redirects: Redirect;
    search: Search;
    'payload-jobs': PayloadJob;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {
    agencies: {
      agents: 'agents';
    };
    artists: {
      events: 'events';
    };
    eventBrands: {
      residencies: 'residencies';
      events: 'events';
    };
    festivalProfiles: {
      festivalEditions: 'festivals';
    };
    festivals: {
      festivalEditionEvents: 'events';
    };
    hubs: {
      childHubs: 'hubs';
      venues: 'venues';
    };
    managers: {
      artists: 'artists';
    };
    managmentCompanies: {
      managers: 'managers';
    };
    residencies: {
      events: 'events';
    };
    tickets: {
      order: 'orders';
    };
    ticketTypes: {
      tickets: 'tickets';
    };
  };
  collectionsSelect: {
    agencies: AgenciesSelect<false> | AgenciesSelect<true>;
    agents: AgentsSelect<false> | AgentsSelect<true>;
    articles: ArticlesSelect<false> | ArticlesSelect<true>;
    artistDeals: ArtistDealsSelect<false> | ArtistDealsSelect<true>;
    artists: ArtistsSelect<false> | ArtistsSelect<true>;
    authors: AuthorsSelect<false> | AuthorsSelect<true>;
    countries: CountriesSelect<false> | CountriesSelect<true>;
    documents: DocumentsSelect<false> | DocumentsSelect<true>;
    eventBrands: EventBrandsSelect<false> | EventBrandsSelect<true>;
    eventOrganizers: EventOrganizersSelect<false> | EventOrganizersSelect<true>;
    events: EventsSelect<false> | EventsSelect<true>;
    fanNotificationListners: FanNotificationListnersSelect<false> | FanNotificationListnersSelect<true>;
    fanUsers: FanUsersSelect<false> | FanUsersSelect<true>;
    festivalProfiles: FestivalProfilesSelect<false> | FestivalProfilesSelect<true>;
    festivals: FestivalsSelect<false> | FestivalsSelect<true>;
    genres: GenresSelect<false> | GenresSelect<true>;
    hubs: HubsSelect<false> | HubsSelect<true>;
    managers: ManagersSelect<false> | ManagersSelect<true>;
    managmentCompanies: ManagmentCompaniesSelect<false> | ManagmentCompaniesSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    ochoEpisodes: OchoEpisodesSelect<false> | OchoEpisodesSelect<true>;
    orders: OrdersSelect<false> | OrdersSelect<true>;
    pages: PagesSelect<false> | PagesSelect<true>;
    promotions: PromotionsSelect<false> | PromotionsSelect<true>;
    residencies: ResidenciesSelect<false> | ResidenciesSelect<true>;
    tickets: TicketsSelect<false> | TicketsSelect<true>;
    ticketTypes: TicketTypesSelect<false> | TicketTypesSelect<true>;
    users: UsersSelect<false> | UsersSelect<true>;
    venues: VenuesSelect<false> | VenuesSelect<true>;
    redirects: RedirectsSelect<false> | RedirectsSelect<true>;
    search: SearchSelect<false> | SearchSelect<true>;
    'payload-jobs': PayloadJobsSelect<false> | PayloadJobsSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: {
      importDiceEvent: TaskImportDiceEvent;
      extractEventUrls: TaskExtractEventUrls;
      getSitemapUrls: TaskGetSitemapUrls;
      qflowLogin: TaskQflowLogin;
      qflowFetchEvents: TaskQflowFetchEvents;
      qflowSyncAttendees: TaskQflowSyncAttendees;
      fetchEventWithTicketTypes: TaskFetchEventWithTicketTypes;
      upsertTicketTypes: TaskUpsertTicketTypes;
      fetchOrders: TaskFetchOrders;
      upsertTickets: TaskUpsertTickets;
      getConcertSitemapUrls: TaskGetConcertSitemapUrls;
      collectConcertUrls: TaskCollectConcertUrls;
      fetchHtmlAndSave: TaskFetchHtmlAndSave;
      saveRawSanityData: TaskSaveRawSanityData;
      importSanityData: TaskImportSanityData;
      handleSongkickEvent: TaskHandleSongkickEvent;
      extractKeysByBucket: TaskExtractKeysByBucket;
      extractLdJson: TaskExtractLdJson;
      extractRetailerId: TaskExtractRetailerId;
      findOrCreateEvent: TaskFindOrCreateEvent;
      findOrCreateTicket: TaskFindOrCreateTicket;
      createOrder: TaskCreateOrder;
      importPlanetscaleUser: TaskImportPlanetscaleUser;
      importPlanetscalePresaleRegistration: TaskImportPlanetscalePresaleRegistration;
      schedulePublish: TaskSchedulePublish;
      inline: {
        input: unknown;
        output: unknown;
      };
    };
    workflows: {
      crawlSanity: WorkflowCrawlSanity;
      crawlRAWorkflow: WorkflowCrawlRAWorkflow;
      crawlSongkickWorkflow: WorkflowCrawlSongkickWorkflow;
      crawlDicekWorkflow: WorkflowCrawlDicekWorkflow;
      importSanityDataWorkflow: WorkflowImportSanityDataWorkflow;
      syncDicePartnerEventsWorkflow: WorkflowSyncDicePartnerEventsWorkflow;
      syncDiceOrdersWorkflow: WorkflowSyncDiceOrdersWorkflow;
      syncDiceWorkflow: WorkflowSyncDiceWorkflow;
      syncQFlowWorkflow: WorkflowSyncQFlowWorkflow;
      syncSongkickWorkflow: WorkflowSyncSongkickWorkflow;
      syncPlanetscaleUsersWorkflow: WorkflowSyncPlanetscaleUsersWorkflow;
      syncPlanetscalePresaleRegistrationsWorkflow: WorkflowSyncPlanetscalePresaleRegistrationsWorkflow;
    };
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "agencies".
 */
export interface Agency {
  id: number;
  name: string;
  address: string;
  generalContacInfo: {
    email: string;
    phoneNumber: string;
  };
  agents?: {
    docs?: (number | Agent)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "agents".
 */
export interface Agent {
  id: number;
  name: string;
  title: string;
  generalContacInfo: {
    email: string;
    phoneNumber: string;
  };
  agency: number | Agency;
  automatedReportingEnabled?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "articles".
 */
export interface Article {
  id: number;
  syncLock?: boolean | null;
  type: 'Academy' | 'Magazine';
  title: string;
  author: number | Author;
  previewImage?: (number | null) | Media;
  mentions?:
    | (
        | {
            relationTo: 'events';
            value: number | Event;
          }
        | {
            relationTo: 'artists';
            value: number | Artist;
          }
        | {
            relationTo: 'eventBrands';
            value: number | EventBrand;
          }
        | {
            relationTo: 'festivals';
            value: number | Festival;
          }
        | {
            relationTo: 'residencies';
            value: number | Residency;
          }
        | {
            relationTo: 'venues';
            value: number | Venue;
          }
        | {
            relationTo: 'authors';
            value: number | Author;
          }
        | {
            relationTo: 'ochoEpisodes';
            value: number | OchoEpisode;
          }
      )[]
    | null;
  hubs?: (number | Hub)[] | null;
  overview?: {
    overview?: {
      hero?: (number | null) | Media;
      content?: {
        root: {
          type: string;
          children: {
            type: string;
            version: number;
            [k: string]: unknown;
          }[];
          direction: ('ltr' | 'rtl') | null;
          format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
          indent: number;
          version: number;
        };
        [k: string]: unknown;
      } | null;
    };
  };
  meta?: {
    meta?: {
      title?: string | null;
      /**
       * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
       */
      image?: (number | null) | Media;
      description?: string | null;
    };
  };
  /**
   * Auto-generated. Uncheck to edit manually.
   */
  slug: string;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "authors".
 */
export interface Author {
  id: number;
  name: string;
  country?: (number | null) | Country;
  previewImage?: (number | null) | Media;
  socialLinks?:
    | {
        resource:
          | 'Facebook'
          | 'Instagram'
          | 'TikTok'
          | 'SoundCloud'
          | 'Discord'
          | 'YouTube'
          | 'Twitter'
          | 'Twitch'
          | 'Pinterest'
          | 'Spotify'
          | 'BeatPort'
          | 'Website'
          | 'Dice'
          | 'TVMaze'
          | 'MusicBrainz'
          | 'Tunefind'
          | 'Line'
          | 'Genius'
          | 'Pandora'
          | 'Shazam'
          | 'Tidal'
          | 'LastFm'
          | 'Deezer'
          | 'Songkick'
          | 'Bandsintown'
          | 'Discogs'
          | 'Itunes'
          | 'Amazon';
        link: string;
        id?: string | null;
      }[]
    | null;
  overview?: {
    overview?: {
      hero?: (number | null) | Media;
      content?: {
        root: {
          type: string;
          children: {
            type: string;
            version: number;
            [k: string]: unknown;
          }[];
          direction: ('ltr' | 'rtl') | null;
          format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
          indent: number;
          version: number;
        };
        [k: string]: unknown;
      } | null;
    };
  };
  meta?: {
    meta?: {
      title?: string | null;
      /**
       * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
       */
      image?: (number | null) | Media;
      description?: string | null;
    };
  };
  /**
   * Auto-generated. Uncheck to edit manually.
   */
  slug: string;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "countries".
 */
export interface Country {
  id: number;
  name: string;
  code: string;
  flag: number | Media;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  alt?: string | null;
  caption?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  prefix?: string | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    square?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    small?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    medium?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    large?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    xlarge?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    og?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "events".
 */
export interface Event {
  id: number;
  syncLock?: boolean | null;
  search?: string | null;
  name: string;
  eventOrganizer?: (number | null) | EventOrganizer;
  origin?: ('sanity' | 'dice' | 'ra' | 'songkick' | 'eventbrite') | null;
  externalPlatformSourceUrls?:
    | {
        platform: 'sanity' | 'dice' | 'ra' | 'songkick' | 'eventbrite';
        sourceUrl: string;
        id?: string | null;
      }[]
    | null;
  externalSanityId?: string | null;
  externalDiceId?: string | null;
  externalResidentAdvisorId?: string | null;
  externalEventBriteId?: string | null;
  ticketUrls?: string[] | null;
  festival?: (number | null) | Festival;
  eventBrand?: (number | null) | EventBrand;
  residency?: (number | null) | Residency;
  Location?: {
    /**
     * Choose where the event happens
     */
    locationType?: ('venue' | 'hub') | null;
    venue?: (number | null) | Venue;
    hub?: (number | null) | Hub;
  };
  /**
   * IANA name, e.g. Europe/Paris or America/Los_Angeles
   */
  timezone?:
    | (
        | 'Africa/Abidjan'
        | 'Africa/Accra'
        | 'Africa/Addis_Ababa'
        | 'Africa/Algiers'
        | 'Africa/Asmera'
        | 'Africa/Bamako'
        | 'Africa/Bangui'
        | 'Africa/Banjul'
        | 'Africa/Bissau'
        | 'Africa/Blantyre'
        | 'Africa/Brazzaville'
        | 'Africa/Bujumbura'
        | 'Africa/Cairo'
        | 'Africa/Casablanca'
        | 'Africa/Ceuta'
        | 'Africa/Conakry'
        | 'Africa/Dakar'
        | 'Africa/Dar_es_Salaam'
        | 'Africa/Djibouti'
        | 'Africa/Douala'
        | 'Africa/El_Aaiun'
        | 'Africa/Freetown'
        | 'Africa/Gaborone'
        | 'Africa/Harare'
        | 'Africa/Johannesburg'
        | 'Africa/Juba'
        | 'Africa/Kampala'
        | 'Africa/Khartoum'
        | 'Africa/Kigali'
        | 'Africa/Kinshasa'
        | 'Africa/Lagos'
        | 'Africa/Libreville'
        | 'Africa/Lome'
        | 'Africa/Luanda'
        | 'Africa/Lubumbashi'
        | 'Africa/Lusaka'
        | 'Africa/Malabo'
        | 'Africa/Maputo'
        | 'Africa/Maseru'
        | 'Africa/Mbabane'
        | 'Africa/Mogadishu'
        | 'Africa/Monrovia'
        | 'Africa/Nairobi'
        | 'Africa/Ndjamena'
        | 'Africa/Niamey'
        | 'Africa/Nouakchott'
        | 'Africa/Ouagadougou'
        | 'Africa/Porto-Novo'
        | 'Africa/Sao_Tome'
        | 'Africa/Tripoli'
        | 'Africa/Tunis'
        | 'Africa/Windhoek'
        | 'America/Adak'
        | 'America/Anchorage'
        | 'America/Anguilla'
        | 'America/Antigua'
        | 'America/Araguaina'
        | 'America/Argentina/La_Rioja'
        | 'America/Argentina/Rio_Gallegos'
        | 'America/Argentina/Salta'
        | 'America/Argentina/San_Juan'
        | 'America/Argentina/San_Luis'
        | 'America/Argentina/Tucuman'
        | 'America/Argentina/Ushuaia'
        | 'America/Aruba'
        | 'America/Asuncion'
        | 'America/Bahia'
        | 'America/Bahia_Banderas'
        | 'America/Barbados'
        | 'America/Belem'
        | 'America/Belize'
        | 'America/Blanc-Sablon'
        | 'America/Boa_Vista'
        | 'America/Bogota'
        | 'America/Boise'
        | 'America/Buenos_Aires'
        | 'America/Cambridge_Bay'
        | 'America/Campo_Grande'
        | 'America/Cancun'
        | 'America/Caracas'
        | 'America/Catamarca'
        | 'America/Cayenne'
        | 'America/Cayman'
        | 'America/Chicago'
        | 'America/Chihuahua'
        | 'America/Ciudad_Juarez'
        | 'America/Coral_Harbour'
        | 'America/Cordoba'
        | 'America/Costa_Rica'
        | 'America/Creston'
        | 'America/Cuiaba'
        | 'America/Curacao'
        | 'America/Danmarkshavn'
        | 'America/Dawson'
        | 'America/Dawson_Creek'
        | 'America/Denver'
        | 'America/Detroit'
        | 'America/Dominica'
        | 'America/Edmonton'
        | 'America/Eirunepe'
        | 'America/El_Salvador'
        | 'America/Fort_Nelson'
        | 'America/Fortaleza'
        | 'America/Glace_Bay'
        | 'America/Godthab'
        | 'America/Goose_Bay'
        | 'America/Grand_Turk'
        | 'America/Grenada'
        | 'America/Guadeloupe'
        | 'America/Guatemala'
        | 'America/Guayaquil'
        | 'America/Guyana'
        | 'America/Halifax'
        | 'America/Havana'
        | 'America/Hermosillo'
        | 'America/Indiana/Knox'
        | 'America/Indiana/Marengo'
        | 'America/Indiana/Petersburg'
        | 'America/Indiana/Tell_City'
        | 'America/Indiana/Vevay'
        | 'America/Indiana/Vincennes'
        | 'America/Indiana/Winamac'
        | 'America/Indianapolis'
        | 'America/Inuvik'
        | 'America/Iqaluit'
        | 'America/Jamaica'
        | 'America/Jujuy'
        | 'America/Juneau'
        | 'America/Kentucky/Monticello'
        | 'America/Kralendijk'
        | 'America/La_Paz'
        | 'America/Lima'
        | 'America/Los_Angeles'
        | 'America/Louisville'
        | 'America/Lower_Princes'
        | 'America/Maceio'
        | 'America/Managua'
        | 'America/Manaus'
        | 'America/Marigot'
        | 'America/Martinique'
        | 'America/Matamoros'
        | 'America/Mazatlan'
        | 'America/Mendoza'
        | 'America/Menominee'
        | 'America/Merida'
        | 'America/Metlakatla'
        | 'America/Mexico_City'
        | 'America/Miquelon'
        | 'America/Moncton'
        | 'America/Monterrey'
        | 'America/Montevideo'
        | 'America/Montserrat'
        | 'America/Nassau'
        | 'America/New_York'
        | 'America/Nome'
        | 'America/Noronha'
        | 'America/North_Dakota/Beulah'
        | 'America/North_Dakota/Center'
        | 'America/North_Dakota/New_Salem'
        | 'America/Ojinaga'
        | 'America/Panama'
        | 'America/Paramaribo'
        | 'America/Phoenix'
        | 'America/Port-au-Prince'
        | 'America/Port_of_Spain'
        | 'America/Porto_Velho'
        | 'America/Puerto_Rico'
        | 'America/Punta_Arenas'
        | 'America/Rankin_Inlet'
        | 'America/Recife'
        | 'America/Regina'
        | 'America/Resolute'
        | 'America/Rio_Branco'
        | 'America/Santarem'
        | 'America/Santiago'
        | 'America/Santo_Domingo'
        | 'America/Sao_Paulo'
        | 'America/Scoresbysund'
        | 'America/Sitka'
        | 'America/St_Barthelemy'
        | 'America/St_Johns'
        | 'America/St_Kitts'
        | 'America/St_Lucia'
        | 'America/St_Thomas'
        | 'America/St_Vincent'
        | 'America/Swift_Current'
        | 'America/Tegucigalpa'
        | 'America/Thule'
        | 'America/Tijuana'
        | 'America/Toronto'
        | 'America/Tortola'
        | 'America/Vancouver'
        | 'America/Whitehorse'
        | 'America/Winnipeg'
        | 'America/Yakutat'
        | 'Antarctica/Casey'
        | 'Antarctica/Davis'
        | 'Antarctica/DumontDUrville'
        | 'Antarctica/Macquarie'
        | 'Antarctica/Mawson'
        | 'Antarctica/McMurdo'
        | 'Antarctica/Palmer'
        | 'Antarctica/Rothera'
        | 'Antarctica/Syowa'
        | 'Antarctica/Troll'
        | 'Antarctica/Vostok'
        | 'Arctic/Longyearbyen'
        | 'Asia/Aden'
        | 'Asia/Almaty'
        | 'Asia/Amman'
        | 'Asia/Anadyr'
        | 'Asia/Aqtau'
        | 'Asia/Aqtobe'
        | 'Asia/Ashgabat'
        | 'Asia/Atyrau'
        | 'Asia/Baghdad'
        | 'Asia/Bahrain'
        | 'Asia/Baku'
        | 'Asia/Bangkok'
        | 'Asia/Barnaul'
        | 'Asia/Beirut'
        | 'Asia/Bishkek'
        | 'Asia/Brunei'
        | 'Asia/Calcutta'
        | 'Asia/Chita'
        | 'Asia/Colombo'
        | 'Asia/Damascus'
        | 'Asia/Dhaka'
        | 'Asia/Dili'
        | 'Asia/Dubai'
        | 'Asia/Dushanbe'
        | 'Asia/Famagusta'
        | 'Asia/Gaza'
        | 'Asia/Hebron'
        | 'Asia/Hong_Kong'
        | 'Asia/Hovd'
        | 'Asia/Irkutsk'
        | 'Asia/Jakarta'
        | 'Asia/Jayapura'
        | 'Asia/Jerusalem'
        | 'Asia/Kabul'
        | 'Asia/Kamchatka'
        | 'Asia/Karachi'
        | 'Asia/Katmandu'
        | 'Asia/Khandyga'
        | 'Asia/Krasnoyarsk'
        | 'Asia/Kuala_Lumpur'
        | 'Asia/Kuching'
        | 'Asia/Kuwait'
        | 'Asia/Macau'
        | 'Asia/Magadan'
        | 'Asia/Makassar'
        | 'Asia/Manila'
        | 'Asia/Muscat'
        | 'Asia/Nicosia'
        | 'Asia/Novokuznetsk'
        | 'Asia/Novosibirsk'
        | 'Asia/Omsk'
        | 'Asia/Oral'
        | 'Asia/Phnom_Penh'
        | 'Asia/Pontianak'
        | 'Asia/Pyongyang'
        | 'Asia/Qatar'
        | 'Asia/Qostanay'
        | 'Asia/Qyzylorda'
        | 'Asia/Rangoon'
        | 'Asia/Riyadh'
        | 'Asia/Saigon'
        | 'Asia/Sakhalin'
        | 'Asia/Samarkand'
        | 'Asia/Seoul'
        | 'Asia/Shanghai'
        | 'Asia/Singapore'
        | 'Asia/Srednekolymsk'
        | 'Asia/Taipei'
        | 'Asia/Tashkent'
        | 'Asia/Tbilisi'
        | 'Asia/Tehran'
        | 'Asia/Thimphu'
        | 'Asia/Tokyo'
        | 'Asia/Tomsk'
        | 'Asia/Ulaanbaatar'
        | 'Asia/Urumqi'
        | 'Asia/Ust-Nera'
        | 'Asia/Vientiane'
        | 'Asia/Vladivostok'
        | 'Asia/Yakutsk'
        | 'Asia/Yekaterinburg'
        | 'Asia/Yerevan'
        | 'Atlantic/Azores'
        | 'Atlantic/Bermuda'
        | 'Atlantic/Canary'
        | 'Atlantic/Cape_Verde'
        | 'Atlantic/Faeroe'
        | 'Atlantic/Madeira'
        | 'Atlantic/Reykjavik'
        | 'Atlantic/South_Georgia'
        | 'Atlantic/St_Helena'
        | 'Atlantic/Stanley'
        | 'Australia/Adelaide'
        | 'Australia/Brisbane'
        | 'Australia/Broken_Hill'
        | 'Australia/Darwin'
        | 'Australia/Eucla'
        | 'Australia/Hobart'
        | 'Australia/Lindeman'
        | 'Australia/Lord_Howe'
        | 'Australia/Melbourne'
        | 'Australia/Perth'
        | 'Australia/Sydney'
        | 'Europe/Amsterdam'
        | 'Europe/Andorra'
        | 'Europe/Astrakhan'
        | 'Europe/Athens'
        | 'Europe/Belgrade'
        | 'Europe/Berlin'
        | 'Europe/Bratislava'
        | 'Europe/Brussels'
        | 'Europe/Bucharest'
        | 'Europe/Budapest'
        | 'Europe/Busingen'
        | 'Europe/Chisinau'
        | 'Europe/Copenhagen'
        | 'Europe/Dublin'
        | 'Europe/Gibraltar'
        | 'Europe/Guernsey'
        | 'Europe/Helsinki'
        | 'Europe/Isle_of_Man'
        | 'Europe/Istanbul'
        | 'Europe/Jersey'
        | 'Europe/Kaliningrad'
        | 'Europe/Kiev'
        | 'Europe/Kirov'
        | 'Europe/Lisbon'
        | 'Europe/Ljubljana'
        | 'Europe/London'
        | 'Europe/Luxembourg'
        | 'Europe/Madrid'
        | 'Europe/Malta'
        | 'Europe/Mariehamn'
        | 'Europe/Minsk'
        | 'Europe/Monaco'
        | 'Europe/Moscow'
        | 'Europe/Oslo'
        | 'Europe/Paris'
        | 'Europe/Podgorica'
        | 'Europe/Prague'
        | 'Europe/Riga'
        | 'Europe/Rome'
        | 'Europe/Samara'
        | 'Europe/San_Marino'
        | 'Europe/Sarajevo'
        | 'Europe/Saratov'
        | 'Europe/Simferopol'
        | 'Europe/Skopje'
        | 'Europe/Sofia'
        | 'Europe/Stockholm'
        | 'Europe/Tallinn'
        | 'Europe/Tirane'
        | 'Europe/Ulyanovsk'
        | 'Europe/Vaduz'
        | 'Europe/Vatican'
        | 'Europe/Vienna'
        | 'Europe/Vilnius'
        | 'Europe/Volgograd'
        | 'Europe/Warsaw'
        | 'Europe/Zagreb'
        | 'Europe/Zurich'
        | 'Indian/Antananarivo'
        | 'Indian/Chagos'
        | 'Indian/Christmas'
        | 'Indian/Cocos'
        | 'Indian/Comoro'
        | 'Indian/Kerguelen'
        | 'Indian/Mahe'
        | 'Indian/Maldives'
        | 'Indian/Mauritius'
        | 'Indian/Mayotte'
        | 'Indian/Reunion'
        | 'Pacific/Apia'
        | 'Pacific/Auckland'
        | 'Pacific/Bougainville'
        | 'Pacific/Chatham'
        | 'Pacific/Easter'
        | 'Pacific/Efate'
        | 'Pacific/Enderbury'
        | 'Pacific/Fakaofo'
        | 'Pacific/Fiji'
        | 'Pacific/Funafuti'
        | 'Pacific/Galapagos'
        | 'Pacific/Gambier'
        | 'Pacific/Guadalcanal'
        | 'Pacific/Guam'
        | 'Pacific/Honolulu'
        | 'Pacific/Kiritimati'
        | 'Pacific/Kosrae'
        | 'Pacific/Kwajalein'
        | 'Pacific/Majuro'
        | 'Pacific/Marquesas'
        | 'Pacific/Midway'
        | 'Pacific/Nauru'
        | 'Pacific/Niue'
        | 'Pacific/Norfolk'
        | 'Pacific/Noumea'
        | 'Pacific/Pago_Pago'
        | 'Pacific/Palau'
        | 'Pacific/Pitcairn'
        | 'Pacific/Ponape'
        | 'Pacific/Port_Moresby'
        | 'Pacific/Rarotonga'
        | 'Pacific/Saipan'
        | 'Pacific/Tahiti'
        | 'Pacific/Tarawa'
        | 'Pacific/Tongatapu'
        | 'Pacific/Truk'
        | 'Pacific/Wake'
        | 'Pacific/Wallis'
      )
    | null;
  announcementDate?: string | null;
  saleOnDate?: string | null;
  saleOffDate?: string | null;
  startDate: string;
  endDate: string;
  lineup?:
    | {
        artist: number | Artist;
        tier?: ('Headliner' | 'SpecialGuest' | 'SupportI' | 'SupportII' | 'Resident' | 'Local' | 'JrLocal') | null;
        startTime?: string | null;
        id?: string | null;
      }[]
    | null;
  eventGenres?: {
    fromLineup?: (number | Genre)[] | null;
    manual?: (number | Genre)[] | null;
  };
  previewImage?: (number | null) | Media;
  socialLinks?:
    | {
        resource:
          | 'Facebook'
          | 'Instagram'
          | 'TikTok'
          | 'SoundCloud'
          | 'Discord'
          | 'YouTube'
          | 'Twitter'
          | 'Twitch'
          | 'Pinterest'
          | 'Spotify'
          | 'BeatPort'
          | 'Website'
          | 'Dice'
          | 'TVMaze'
          | 'MusicBrainz'
          | 'Tunefind'
          | 'Line'
          | 'Genius'
          | 'Pandora'
          | 'Shazam'
          | 'Tidal'
          | 'LastFm'
          | 'Deezer'
          | 'Songkick'
          | 'Bandsintown'
          | 'Discogs'
          | 'Itunes'
          | 'Amazon';
        link: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Select events that act as after‑parties for this event
   */
  afterparties?: (number | Event)[] | null;
  /**
   * Frequently‑asked questions for this event
   */
  faqs?:
    | {
        question: string;
        answer: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        };
        id?: string | null;
      }[]
    | null;
  /**
   * Minimum attendee age (in years). Leave blank if no restriction.
   */
  minAge?: number | null;
  ticketTypes?:
    | {
        ticketType: number | TicketType;
        id?: string | null;
      }[]
    | null;
  overview?: {
    overview?: {
      hero?: (number | null) | Media;
      content?: {
        root: {
          type: string;
          children: {
            type: string;
            version: number;
            [k: string]: unknown;
          }[];
          direction: ('ltr' | 'rtl') | null;
          format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
          indent: number;
          version: number;
        };
        [k: string]: unknown;
      } | null;
    };
  };
  marketing?: {
    trackingLinks?:
      | {
          platform?: ('sanity' | 'dice' | 'ra' | 'songkick' | 'eventbrite') | null;
          channel: string;
          dealsParams?: ('organic' | 'paid') | null;
          campaign: string;
          link: string;
          id?: string | null;
        }[]
      | null;
  };
  promotions?: {
    promotions?: (number | Promotion)[] | null;
  };
  meta?: {
    meta?: {
      title?: string | null;
      /**
       * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
       */
      image?: (number | null) | Media;
      description?: string | null;
    };
  };
  /**
   * Auto-generated. Uncheck to edit manually.
   */
  slug: string;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "eventOrganizers".
 */
export interface EventOrganizer {
  id: number;
  syncLock?: boolean | null;
  name: string;
  presentedBy?: string | null;
  diceCredentials?: {
    login?: string | null;
    password?: string | null;
    DICE_PARTNER_API_TOKEN?: string | null;
  };
  residentAdvisorCredentials?: {
    login?: string | null;
    password?: string | null;
  };
  qflowCredentials?: {
    login?: string | null;
    password?: string | null;
  };
  overview?: {
    overview?: {
      hero?: (number | null) | Media;
      content?: {
        root: {
          type: string;
          children: {
            type: string;
            version: number;
            [k: string]: unknown;
          }[];
          direction: ('ltr' | 'rtl') | null;
          format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
          indent: number;
          version: number;
        };
        [k: string]: unknown;
      } | null;
    };
  };
  meta?: {
    meta?: {
      title?: string | null;
      /**
       * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
       */
      image?: (number | null) | Media;
      description?: string | null;
    };
  };
  /**
   * Auto-generated. Uncheck to edit manually.
   */
  slug: string;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "festivals".
 */
export interface Festival {
  id: number;
  syncLock?: boolean | null;
  name: string;
  festivalBrand?: (number | null) | FestivalProfile;
  festivalEditionEvents?: {
    docs?: (number | Event)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  previewImage?: (number | null) | Media;
  socialLinks?:
    | {
        resource:
          | 'Facebook'
          | 'Instagram'
          | 'TikTok'
          | 'SoundCloud'
          | 'Discord'
          | 'YouTube'
          | 'Twitter'
          | 'Twitch'
          | 'Pinterest'
          | 'Spotify'
          | 'BeatPort'
          | 'Website'
          | 'Dice'
          | 'TVMaze'
          | 'MusicBrainz'
          | 'Tunefind'
          | 'Line'
          | 'Genius'
          | 'Pandora'
          | 'Shazam'
          | 'Tidal'
          | 'LastFm'
          | 'Deezer'
          | 'Songkick'
          | 'Bandsintown'
          | 'Discogs'
          | 'Itunes'
          | 'Amazon';
        link: string;
        id?: string | null;
      }[]
    | null;
  externalSanityId?: string | null;
  origin?: ('sanity' | 'dice' | 'ra' | 'songkick' | 'eventbrite') | null;
  overview?: {
    overview?: {
      hero?: (number | null) | Media;
      content?: {
        root: {
          type: string;
          children: {
            type: string;
            version: number;
            [k: string]: unknown;
          }[];
          direction: ('ltr' | 'rtl') | null;
          format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
          indent: number;
          version: number;
        };
        [k: string]: unknown;
      } | null;
    };
  };
  meta?: {
    meta?: {
      title?: string | null;
      /**
       * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
       */
      image?: (number | null) | Media;
      description?: string | null;
    };
  };
  /**
   * Auto-generated. Uncheck to edit manually.
   */
  slug: string;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "festivalProfiles".
 */
export interface FestivalProfile {
  id: number;
  syncLock?: boolean | null;
  name: string;
  festivalEditions?: {
    docs?: (number | Festival)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  previewImage?: (number | null) | Media;
  socialLinks?:
    | {
        resource:
          | 'Facebook'
          | 'Instagram'
          | 'TikTok'
          | 'SoundCloud'
          | 'Discord'
          | 'YouTube'
          | 'Twitter'
          | 'Twitch'
          | 'Pinterest'
          | 'Spotify'
          | 'BeatPort'
          | 'Website'
          | 'Dice'
          | 'TVMaze'
          | 'MusicBrainz'
          | 'Tunefind'
          | 'Line'
          | 'Genius'
          | 'Pandora'
          | 'Shazam'
          | 'Tidal'
          | 'LastFm'
          | 'Deezer'
          | 'Songkick'
          | 'Bandsintown'
          | 'Discogs'
          | 'Itunes'
          | 'Amazon';
        link: string;
        id?: string | null;
      }[]
    | null;
  externalSanityId?: string | null;
  origin?: ('sanity' | 'dice' | 'ra' | 'songkick' | 'eventbrite') | null;
  overview?: {
    overview?: {
      hero?: (number | null) | Media;
      content?: {
        root: {
          type: string;
          children: {
            type: string;
            version: number;
            [k: string]: unknown;
          }[];
          direction: ('ltr' | 'rtl') | null;
          format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
          indent: number;
          version: number;
        };
        [k: string]: unknown;
      } | null;
    };
  };
  meta?: {
    meta?: {
      title?: string | null;
      /**
       * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
       */
      image?: (number | null) | Media;
      description?: string | null;
    };
  };
  /**
   * Auto-generated. Uncheck to edit manually.
   */
  slug: string;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "eventBrands".
 */
export interface EventBrand {
  id: number;
  syncLock?: boolean | null;
  name: string;
  country?: (number | null) | Country;
  residencies?: {
    docs?: (number | Residency)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  events?: {
    docs?: (number | Event)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  previewImage?: (number | null) | Media;
  socialLinks?:
    | {
        resource:
          | 'Facebook'
          | 'Instagram'
          | 'TikTok'
          | 'SoundCloud'
          | 'Discord'
          | 'YouTube'
          | 'Twitter'
          | 'Twitch'
          | 'Pinterest'
          | 'Spotify'
          | 'BeatPort'
          | 'Website'
          | 'Dice'
          | 'TVMaze'
          | 'MusicBrainz'
          | 'Tunefind'
          | 'Line'
          | 'Genius'
          | 'Pandora'
          | 'Shazam'
          | 'Tidal'
          | 'LastFm'
          | 'Deezer'
          | 'Songkick'
          | 'Bandsintown'
          | 'Discogs'
          | 'Itunes'
          | 'Amazon';
        link: string;
        id?: string | null;
      }[]
    | null;
  overview?: {
    overview?: {
      hero?: (number | null) | Media;
      content?: {
        root: {
          type: string;
          children: {
            type: string;
            version: number;
            [k: string]: unknown;
          }[];
          direction: ('ltr' | 'rtl') | null;
          format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
          indent: number;
          version: number;
        };
        [k: string]: unknown;
      } | null;
    };
  };
  meta?: {
    meta?: {
      title?: string | null;
      /**
       * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
       */
      image?: (number | null) | Media;
      description?: string | null;
    };
  };
  /**
   * Auto-generated. Uncheck to edit manually.
   */
  slug: string;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "residencies".
 */
export interface Residency {
  id: number;
  syncLock?: boolean | null;
  name: string;
  eventBrand?: (number | null) | EventBrand;
  events?: {
    docs?: (number | Event)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  Location?: {
    venue?: (number | null) | Venue;
    geoLocation?: {
      locationName?: string | null;
      link?: string | null;
    };
  };
  previewImage?: (number | null) | Media;
  socialLinks?:
    | {
        resource:
          | 'Facebook'
          | 'Instagram'
          | 'TikTok'
          | 'SoundCloud'
          | 'Discord'
          | 'YouTube'
          | 'Twitter'
          | 'Twitch'
          | 'Pinterest'
          | 'Spotify'
          | 'BeatPort'
          | 'Website'
          | 'Dice'
          | 'TVMaze'
          | 'MusicBrainz'
          | 'Tunefind'
          | 'Line'
          | 'Genius'
          | 'Pandora'
          | 'Shazam'
          | 'Tidal'
          | 'LastFm'
          | 'Deezer'
          | 'Songkick'
          | 'Bandsintown'
          | 'Discogs'
          | 'Itunes'
          | 'Amazon';
        link: string;
        id?: string | null;
      }[]
    | null;
  overview?: {
    overview?: {
      hero?: (number | null) | Media;
      content?: {
        root: {
          type: string;
          children: {
            type: string;
            version: number;
            [k: string]: unknown;
          }[];
          direction: ('ltr' | 'rtl') | null;
          format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
          indent: number;
          version: number;
        };
        [k: string]: unknown;
      } | null;
    };
  };
  meta?: {
    meta?: {
      title?: string | null;
      /**
       * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
       */
      image?: (number | null) | Media;
      description?: string | null;
    };
  };
  /**
   * Auto-generated. Uncheck to edit manually.
   */
  slug: string;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "venues".
 */
export interface Venue {
  id: number;
  syncLock?: boolean | null;
  name: string;
  address: string;
  city: string;
  country: string;
  /**
   * @minItems 2
   * @maxItems 2
   */
  coordinates?: [number, number] | null;
  timezone: string;
  hub?: (number | null) | Hub;
  capacities?:
    | {
        title: string;
        capacity: number;
        id?: string | null;
      }[]
    | null;
  previewImage?: (number | null) | Media;
  mediaAssets?: (number | Media)[] | null;
  accessibility?: string[] | null;
  internalContacts?:
    | {
        type?: ('Management' | 'Marketing' | 'Advancing' | 'Production' | 'Ticketing' | 'Finance') | null;
        name: string;
        phoneNumber?: string | null;
        emailAddress?: string | null;
        id?: string | null;
      }[]
    | null;
  houseRules?: (number | null) | Document;
  productionTechSpecs?: (number | Document)[] | null;
  invoicingInfo?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  closestAirport?: string | null;
  socialLinks?:
    | {
        resource:
          | 'Facebook'
          | 'Instagram'
          | 'TikTok'
          | 'SoundCloud'
          | 'Discord'
          | 'YouTube'
          | 'Twitter'
          | 'Twitch'
          | 'Pinterest'
          | 'Spotify'
          | 'BeatPort'
          | 'Website'
          | 'Dice'
          | 'TVMaze'
          | 'MusicBrainz'
          | 'Tunefind'
          | 'Line'
          | 'Genius'
          | 'Pandora'
          | 'Shazam'
          | 'Tidal'
          | 'LastFm'
          | 'Deezer'
          | 'Songkick'
          | 'Bandsintown'
          | 'Discogs'
          | 'Itunes'
          | 'Amazon';
        link: string;
        id?: string | null;
      }[]
    | null;
  origin?: ('sanity' | 'dice' | 'ra' | 'songkick' | 'eventbrite') | null;
  externalSanityId?: string | null;
  overview?: {
    overview?: {
      hero?: (number | null) | Media;
      content?: {
        root: {
          type: string;
          children: {
            type: string;
            version: number;
            [k: string]: unknown;
          }[];
          direction: ('ltr' | 'rtl') | null;
          format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
          indent: number;
          version: number;
        };
        [k: string]: unknown;
      } | null;
    };
  };
  meta?: {
    meta?: {
      title?: string | null;
      /**
       * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
       */
      image?: (number | null) | Media;
      description?: string | null;
    };
  };
  /**
   * Auto-generated. Uncheck to edit manually.
   */
  slug: string;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "hubs".
 */
export interface Hub {
  id: number;
  name: string;
  formatted: string;
  /**
   * Level of this hub in the hierarchy
   */
  type?: ('country' | 'region' | 'city') | null;
  /**
   * Select a parent hub (e.g. region for a city, country for a region)
   */
  parent?: (number | null) | Hub;
  childHubs?: {
    docs?: (number | Hub)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  venues?: {
    docs?: (number | Venue)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  childHubVenues?: (number | Venue)[] | null;
  overview?: {
    overview?: {
      hero?: (number | null) | Media;
      content?: {
        root: {
          type: string;
          children: {
            type: string;
            version: number;
            [k: string]: unknown;
          }[];
          direction: ('ltr' | 'rtl') | null;
          format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
          indent: number;
          version: number;
        };
        [k: string]: unknown;
      } | null;
    };
  };
  meta?: {
    meta?: {
      title?: string | null;
      /**
       * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
       */
      image?: (number | null) | Media;
      description?: string | null;
    };
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "documents".
 */
export interface Document {
  id: number;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "artists".
 */
export interface Artist {
  id: number;
  syncLock?: boolean | null;
  search?: string | null;
  name: string;
  spotifyId?: string | null;
  chartMetricExternalId?: string | null;
  externalSanityId?: string | null;
  country?: (number | null) | Country;
  previewImage?: (number | null) | Media;
  genres?: (number | Genre)[] | null;
  events?: {
    docs?: (number | Event)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  residencies?: (number | Residency)[] | null;
  representation?:
    | {
        territory: number | Country;
        coverage?: ('global' | 'continent' | 'country') | null;
        agency: number | Agency;
        agent?: (number | null) | Agent;
        managementCompany?: (number | null) | ManagmentCompany;
        manager?: (number | null) | Manager;
        id?: string | null;
      }[]
    | null;
  managmentCompanies?: (number | ManagmentCompany)[] | null;
  managers?: (number | Manager)[] | null;
  socialLinks?:
    | {
        resource:
          | 'Facebook'
          | 'Instagram'
          | 'TikTok'
          | 'SoundCloud'
          | 'Discord'
          | 'YouTube'
          | 'Twitter'
          | 'Twitch'
          | 'Pinterest'
          | 'Spotify'
          | 'BeatPort'
          | 'Website'
          | 'Dice'
          | 'TVMaze'
          | 'MusicBrainz'
          | 'Tunefind'
          | 'Line'
          | 'Genius'
          | 'Pandora'
          | 'Shazam'
          | 'Tidal'
          | 'LastFm'
          | 'Deezer'
          | 'Songkick'
          | 'Bandsintown'
          | 'Discogs'
          | 'Itunes'
          | 'Amazon';
        link: string;
        id?: string | null;
      }[]
    | null;
  overview?: {
    overview?: {
      hero?: (number | null) | Media;
      content?: {
        root: {
          type: string;
          children: {
            type: string;
            version: number;
            [k: string]: unknown;
          }[];
          direction: ('ltr' | 'rtl') | null;
          format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
          indent: number;
          version: number;
        };
        [k: string]: unknown;
      } | null;
    };
  };
  meta?: {
    meta?: {
      title?: string | null;
      /**
       * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
       */
      image?: (number | null) | Media;
      description?: string | null;
    };
  };
  /**
   * Auto-generated. Uncheck to edit manually.
   */
  slug: string;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "genres".
 */
export interface Genre {
  id: number;
  name: string;
  type?: ('Spotify' | 'GrayArea' | 'Dice' | 'ResidentAdvisor' | 'ChartMetric') | null;
  description?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  overview?: {
    overview?: {
      hero?: (number | null) | Media;
      content?: {
        root: {
          type: string;
          children: {
            type: string;
            version: number;
            [k: string]: unknown;
          }[];
          direction: ('ltr' | 'rtl') | null;
          format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
          indent: number;
          version: number;
        };
        [k: string]: unknown;
      } | null;
    };
  };
  meta?: {
    meta?: {
      title?: string | null;
      /**
       * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
       */
      image?: (number | null) | Media;
      description?: string | null;
    };
  };
  fullTitle?: string | null;
  /**
   * Auto-generated. Uncheck to edit manually.
   */
  slug: string;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "managmentCompanies".
 */
export interface ManagmentCompany {
  id: number;
  name: string;
  address: string;
  generalContacInfo: {
    email: string;
    phoneNumber: string;
  };
  managers?: {
    docs?: (number | Manager)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "managers".
 */
export interface Manager {
  id: number;
  name: string;
  title: string;
  generalContacInfo: {
    email: string;
    phoneNumber: string;
  };
  managmentCompany: number | ManagmentCompany;
  automatedReportingEnabled?: boolean | null;
  artists?: {
    docs?: (number | Artist)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ticketTypes".
 */
export interface TicketType {
  id: number;
  syncLock?: boolean | null;
  origin?: ('sanity' | 'dice' | 'ra' | 'songkick' | 'eventbrite') | null;
  externalDiceId: {
    /**
     * ID of the parent ticket type
     */
    ticketTypeId: string;
    /**
     * ID of this specific price tier
     */
    priceTierId: string;
  };
  externalResidentAdvisorId?: string | null;
  externalEventBriteId?: string | null;
  /**
   * Name of the ticket type
   */
  ticketTypeName: string;
  /**
   * Label of this price tier (e.g. “Last Chance”)
   */
  label: string;
  /**
   * Parent ticket type description
   */
  description?: string | null;
  /**
   * Is the parent ticket type archived?
   */
  archived?: boolean | null;
  internalPresentationOnly?: boolean | null;
  /**
   * Total tickets allocated for parent type
   */
  allocation: number;
  /**
   * Face value of this tier
   */
  faceValue: number;
  fee?:
    | {
        /**
         * Is this fee applicable?
         */
        applicable?: boolean | null;
        /**
         * Amount of the fee
         */
        amount: number;
        computed: number;
        /**
         * Type of the fee
         */
        type: string;
        unit: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Platform fee portion
   */
  totalFee?: number | null;
  /**
   * Amount paid by fan (price + fee)
   */
  total?: number | null;
  /**
   * All tickets that use this ticket type
   */
  tickets?: {
    docs?: (number | Ticket)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tickets".
 */
export interface Ticket {
  id: number;
  externalDiceId: string;
  admittedDate?: string | null;
  code?: string | null;
  total?: number | null;
  /**
   * Which ticket type (VIP, GA, etc.)
   */
  ticketType: number | TicketType;
  order?: {
    docs?: (number | Order)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "orders".
 */
export interface Order {
  id: number;
  externalDiceId: string;
  origin?: ('sanity' | 'dice' | 'ra' | 'songkick' | 'eventbrite') | null;
  externalResidentAdvisorId?: string | null;
  externalEventBriteId?: string | null;
  event?: (number | null) | Event;
  fanEmail: string;
  fan?: (number | null) | FanUser;
  tickets?: (number | Ticket)[] | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "fanUsers".
 */
export interface FanUser {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string | null;
  phoneNumberCountryCode?: string | null;
  dateOfBirth: string;
  ethWallet?: string | null;
  followingsGroup?: {
    eventBrandsFollowings?: (number | EventBrand)[] | null;
    festivalsFollowings?: (number | Genre)[] | null;
    artistsFollowings?: (number | Artist)[] | null;
    authorsFollowings?: (number | Author)[] | null;
    genresFollowings?: (number | Genre)[] | null;
  };
  orders?: (number | Order)[] | null;
  diceId?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "promotions".
 */
export interface Promotion {
  id: number;
  title: string;
  startDate: string;
  endDate: string;
  maxRedemptions: number;
  codeLocks?:
    | {
        code: string;
        claimedBy?: (number | null) | FanUser;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ochoEpisodes".
 */
export interface OchoEpisode {
  id: number;
  title: string;
  episodeNumber: number;
  description: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  videoUrl?: string | null;
  podcastUrl?: string | null;
  guests: (number | Artist)[];
  previewImage?: (number | null) | Media;
  overview?: {
    overview?: {
      hero?: (number | null) | Media;
      content?: {
        root: {
          type: string;
          children: {
            type: string;
            version: number;
            [k: string]: unknown;
          }[];
          direction: ('ltr' | 'rtl') | null;
          format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
          indent: number;
          version: number;
        };
        [k: string]: unknown;
      } | null;
    };
  };
  meta?: {
    meta?: {
      title?: string | null;
      /**
       * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
       */
      image?: (number | null) | Media;
      description?: string | null;
    };
  };
  /**
   * Auto-generated. Uncheck to edit manually.
   */
  slug: string;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "artistDeals".
 */
export interface ArtistDeal {
  id: number;
  artist: number | Artist;
  event: number | Event;
  statusHistory?:
    | {
        status?: ('offerSent' | 'counterOffer' | 'declined' | 'accepted' | 'canceled') | null;
        description?: string | null;
        id?: string | null;
      }[]
    | null;
  documents?: (number | Document)[] | null;
  expenses: number;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "fanNotificationListners".
 */
export interface FanNotificationListner {
  id: number;
  type: 'eventPresale';
  event: number | Event;
  fan: number | FanUser;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages".
 */
export interface Page {
  id: number;
  title: string;
  layout: (CallToActionBlock | ContentBlock | MediaBlock | ArchiveBlock | EventsBlock | HomeEventsSection)[];
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (number | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CallToActionBlock".
 */
export interface CallToActionBlock {
  richText?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  links?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?: {
            relationTo: 'pages';
            value: number | Page;
          } | null;
          url?: string | null;
          label: string;
          /**
           * Choose how the link should be rendered.
           */
          appearance?: ('default' | 'outline') | null;
        };
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'cta';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentBlock".
 */
export interface ContentBlock {
  columns?:
    | {
        size?: ('oneThird' | 'half' | 'twoThirds' | 'full') | null;
        richText?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        enableLink?: boolean | null;
        link?: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?: {
            relationTo: 'pages';
            value: number | Page;
          } | null;
          url?: string | null;
          label: string;
          /**
           * Choose how the link should be rendered.
           */
          appearance?: ('default' | 'outline') | null;
        };
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'content';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MediaBlock".
 */
export interface MediaBlock {
  media: number | Media;
  id?: string | null;
  blockName?: string | null;
  blockType: 'mediaBlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ArchiveBlock".
 */
export interface ArchiveBlock {
  introContent?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  populateBy?: ('collection' | 'selection') | null;
  relationTo?: 'pages' | null;
  limit?: number | null;
  selectedDocs?:
    | {
        relationTo: 'pages';
        value: number | Page;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'archive';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "eventsBlock".
 */
export interface EventsBlock {
  heading: string;
  events: (number | Event)[];
  id?: string | null;
  blockName?: string | null;
  blockType: 'eventsBlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "homeEventsSection".
 */
export interface HomeEventsSection {
  title: string;
  events: (number | Event)[];
  id?: string | null;
  blockName?: string | null;
  blockType: 'homeEventsSection';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  name?: string | null;
  roles?: ('super-admin' | 'user' | 'read-only')[] | null;
  tenants?:
    | {
        tenant: number | EventOrganizer;
        roles: ('tenant-admin' | 'tenant-viewer')[];
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects".
 */
export interface Redirect {
  id: number;
  /**
   * You will need to rebuild the website when changing this field.
   */
  from: string;
  to?: {
    type?: ('reference' | 'custom') | null;
    reference?: {
      relationTo: 'pages';
      value: number | Page;
    } | null;
    url?: string | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This is a collection of automatically created search results. These results are used by the global site search and will be updated automatically as documents in the CMS are created or updated.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "search".
 */
export interface Search {
  id: number;
  title?: string | null;
  priority?: number | null;
  doc: {
    relationTo: 'articles';
    value: number | Article;
  };
  slug?: string | null;
  meta?: {
    title?: string | null;
    description?: string | null;
    image?: (number | null) | Media;
  };
  categories?:
    | {
        relationTo?: string | null;
        id?: string | null;
        title?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs".
 */
export interface PayloadJob {
  id: number;
  /**
   * Input data provided to the job
   */
  input?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  taskStatus?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  completedAt?: string | null;
  totalTried?: number | null;
  /**
   * If hasError is true this job will not be retried
   */
  hasError?: boolean | null;
  /**
   * If hasError is true, this is the error that caused it
   */
  error?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Task execution log
   */
  log?:
    | {
        executedAt: string;
        completedAt: string;
        taskSlug:
          | 'inline'
          | 'importDiceEvent'
          | 'extractEventUrls'
          | 'getSitemapUrls'
          | 'qflowLogin'
          | 'qflowFetchEvents'
          | 'qflowSyncAttendees'
          | 'fetchEventWithTicketTypes'
          | 'upsertTicketTypes'
          | 'fetchOrders'
          | 'upsertTickets'
          | 'getConcertSitemapUrls'
          | 'collectConcertUrls'
          | 'fetchHtmlAndSave'
          | 'saveRawSanityData'
          | 'importSanityData'
          | 'handleSongkickEvent'
          | 'extractKeysByBucket'
          | 'extractLdJson'
          | 'extractRetailerId'
          | 'findOrCreateEvent'
          | 'findOrCreateTicket'
          | 'createOrder'
          | 'importPlanetscaleUser'
          | 'importPlanetscalePresaleRegistration'
          | 'schedulePublish';
        taskID: string;
        input?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        output?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        state: 'failed' | 'succeeded';
        error?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        id?: string | null;
      }[]
    | null;
  workflowSlug?:
    | (
        | 'crawlSanity'
        | 'crawlRAWorkflow'
        | 'crawlSongkickWorkflow'
        | 'crawlDicekWorkflow'
        | 'importSanityDataWorkflow'
        | 'syncDicePartnerEventsWorkflow'
        | 'syncDiceOrdersWorkflow'
        | 'syncDiceWorkflow'
        | 'syncQFlowWorkflow'
        | 'syncSongkickWorkflow'
        | 'syncPlanetscaleUsersWorkflow'
        | 'syncPlanetscalePresaleRegistrationsWorkflow'
      )
    | null;
  taskSlug?:
    | (
        | 'inline'
        | 'importDiceEvent'
        | 'extractEventUrls'
        | 'getSitemapUrls'
        | 'qflowLogin'
        | 'qflowFetchEvents'
        | 'qflowSyncAttendees'
        | 'fetchEventWithTicketTypes'
        | 'upsertTicketTypes'
        | 'fetchOrders'
        | 'upsertTickets'
        | 'getConcertSitemapUrls'
        | 'collectConcertUrls'
        | 'fetchHtmlAndSave'
        | 'saveRawSanityData'
        | 'importSanityData'
        | 'handleSongkickEvent'
        | 'extractKeysByBucket'
        | 'extractLdJson'
        | 'extractRetailerId'
        | 'findOrCreateEvent'
        | 'findOrCreateTicket'
        | 'createOrder'
        | 'importPlanetscaleUser'
        | 'importPlanetscalePresaleRegistration'
        | 'schedulePublish'
      )
    | null;
  queue?: string | null;
  waitUntil?: string | null;
  processing?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'agencies';
        value: number | Agency;
      } | null)
    | ({
        relationTo: 'agents';
        value: number | Agent;
      } | null)
    | ({
        relationTo: 'articles';
        value: number | Article;
      } | null)
    | ({
        relationTo: 'artistDeals';
        value: number | ArtistDeal;
      } | null)
    | ({
        relationTo: 'artists';
        value: number | Artist;
      } | null)
    | ({
        relationTo: 'authors';
        value: number | Author;
      } | null)
    | ({
        relationTo: 'countries';
        value: number | Country;
      } | null)
    | ({
        relationTo: 'documents';
        value: number | Document;
      } | null)
    | ({
        relationTo: 'eventBrands';
        value: number | EventBrand;
      } | null)
    | ({
        relationTo: 'eventOrganizers';
        value: number | EventOrganizer;
      } | null)
    | ({
        relationTo: 'events';
        value: number | Event;
      } | null)
    | ({
        relationTo: 'fanNotificationListners';
        value: number | FanNotificationListner;
      } | null)
    | ({
        relationTo: 'fanUsers';
        value: number | FanUser;
      } | null)
    | ({
        relationTo: 'festivalProfiles';
        value: number | FestivalProfile;
      } | null)
    | ({
        relationTo: 'festivals';
        value: number | Festival;
      } | null)
    | ({
        relationTo: 'genres';
        value: number | Genre;
      } | null)
    | ({
        relationTo: 'hubs';
        value: number | Hub;
      } | null)
    | ({
        relationTo: 'managers';
        value: number | Manager;
      } | null)
    | ({
        relationTo: 'managmentCompanies';
        value: number | ManagmentCompany;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'ochoEpisodes';
        value: number | OchoEpisode;
      } | null)
    | ({
        relationTo: 'orders';
        value: number | Order;
      } | null)
    | ({
        relationTo: 'pages';
        value: number | Page;
      } | null)
    | ({
        relationTo: 'promotions';
        value: number | Promotion;
      } | null)
    | ({
        relationTo: 'residencies';
        value: number | Residency;
      } | null)
    | ({
        relationTo: 'tickets';
        value: number | Ticket;
      } | null)
    | ({
        relationTo: 'ticketTypes';
        value: number | TicketType;
      } | null)
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'venues';
        value: number | Venue;
      } | null)
    | ({
        relationTo: 'redirects';
        value: number | Redirect;
      } | null)
    | ({
        relationTo: 'search';
        value: number | Search;
      } | null)
    | ({
        relationTo: 'payload-jobs';
        value: number | PayloadJob;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "agencies_select".
 */
export interface AgenciesSelect<T extends boolean = true> {
  name?: T;
  address?: T;
  generalContacInfo?:
    | T
    | {
        email?: T;
        phoneNumber?: T;
      };
  agents?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "agents_select".
 */
export interface AgentsSelect<T extends boolean = true> {
  name?: T;
  title?: T;
  generalContacInfo?:
    | T
    | {
        email?: T;
        phoneNumber?: T;
      };
  agency?: T;
  automatedReportingEnabled?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "articles_select".
 */
export interface ArticlesSelect<T extends boolean = true> {
  syncLock?: T;
  type?: T;
  title?: T;
  author?: T;
  previewImage?: T;
  mentions?: T;
  hubs?: T;
  overview?:
    | T
    | {
        overview?:
          | T
          | {
              hero?: T;
              content?: T;
            };
      };
  meta?:
    | T
    | {
        meta?:
          | T
          | {
              title?: T;
              image?: T;
              description?: T;
            };
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "artistDeals_select".
 */
export interface ArtistDealsSelect<T extends boolean = true> {
  artist?: T;
  event?: T;
  statusHistory?:
    | T
    | {
        status?: T;
        description?: T;
        id?: T;
      };
  documents?: T;
  expenses?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "artists_select".
 */
export interface ArtistsSelect<T extends boolean = true> {
  syncLock?: T;
  search?: T;
  name?: T;
  spotifyId?: T;
  chartMetricExternalId?: T;
  externalSanityId?: T;
  country?: T;
  previewImage?: T;
  genres?: T;
  events?: T;
  residencies?: T;
  representation?:
    | T
    | {
        territory?: T;
        coverage?: T;
        agency?: T;
        agent?: T;
        managementCompany?: T;
        manager?: T;
        id?: T;
      };
  managmentCompanies?: T;
  managers?: T;
  socialLinks?:
    | T
    | {
        resource?: T;
        link?: T;
        id?: T;
      };
  overview?:
    | T
    | {
        overview?:
          | T
          | {
              hero?: T;
              content?: T;
            };
      };
  meta?:
    | T
    | {
        meta?:
          | T
          | {
              title?: T;
              image?: T;
              description?: T;
            };
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "authors_select".
 */
export interface AuthorsSelect<T extends boolean = true> {
  name?: T;
  country?: T;
  previewImage?: T;
  socialLinks?:
    | T
    | {
        resource?: T;
        link?: T;
        id?: T;
      };
  overview?:
    | T
    | {
        overview?:
          | T
          | {
              hero?: T;
              content?: T;
            };
      };
  meta?:
    | T
    | {
        meta?:
          | T
          | {
              title?: T;
              image?: T;
              description?: T;
            };
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "countries_select".
 */
export interface CountriesSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  flag?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "documents_select".
 */
export interface DocumentsSelect<T extends boolean = true> {
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "eventBrands_select".
 */
export interface EventBrandsSelect<T extends boolean = true> {
  syncLock?: T;
  name?: T;
  country?: T;
  residencies?: T;
  events?: T;
  previewImage?: T;
  socialLinks?:
    | T
    | {
        resource?: T;
        link?: T;
        id?: T;
      };
  overview?:
    | T
    | {
        overview?:
          | T
          | {
              hero?: T;
              content?: T;
            };
      };
  meta?:
    | T
    | {
        meta?:
          | T
          | {
              title?: T;
              image?: T;
              description?: T;
            };
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "eventOrganizers_select".
 */
export interface EventOrganizersSelect<T extends boolean = true> {
  syncLock?: T;
  name?: T;
  presentedBy?: T;
  diceCredentials?:
    | T
    | {
        login?: T;
        password?: T;
        DICE_PARTNER_API_TOKEN?: T;
      };
  residentAdvisorCredentials?:
    | T
    | {
        login?: T;
        password?: T;
      };
  qflowCredentials?:
    | T
    | {
        login?: T;
        password?: T;
      };
  overview?:
    | T
    | {
        overview?:
          | T
          | {
              hero?: T;
              content?: T;
            };
      };
  meta?:
    | T
    | {
        meta?:
          | T
          | {
              title?: T;
              image?: T;
              description?: T;
            };
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "events_select".
 */
export interface EventsSelect<T extends boolean = true> {
  syncLock?: T;
  search?: T;
  name?: T;
  eventOrganizer?: T;
  origin?: T;
  externalPlatformSourceUrls?:
    | T
    | {
        platform?: T;
        sourceUrl?: T;
        id?: T;
      };
  externalSanityId?: T;
  externalDiceId?: T;
  externalResidentAdvisorId?: T;
  externalEventBriteId?: T;
  ticketUrls?: T;
  festival?: T;
  eventBrand?: T;
  residency?: T;
  Location?:
    | T
    | {
        locationType?: T;
        venue?: T;
        hub?: T;
      };
  timezone?: T;
  announcementDate?: T;
  saleOnDate?: T;
  saleOffDate?: T;
  startDate?: T;
  endDate?: T;
  lineup?:
    | T
    | {
        artist?: T;
        tier?: T;
        startTime?: T;
        id?: T;
      };
  eventGenres?:
    | T
    | {
        fromLineup?: T;
        manual?: T;
      };
  previewImage?: T;
  socialLinks?:
    | T
    | {
        resource?: T;
        link?: T;
        id?: T;
      };
  afterparties?: T;
  faqs?:
    | T
    | {
        question?: T;
        answer?: T;
        id?: T;
      };
  minAge?: T;
  ticketTypes?:
    | T
    | {
        ticketType?: T;
        id?: T;
      };
  overview?:
    | T
    | {
        overview?:
          | T
          | {
              hero?: T;
              content?: T;
            };
      };
  marketing?:
    | T
    | {
        trackingLinks?:
          | T
          | {
              platform?: T;
              channel?: T;
              dealsParams?: T;
              campaign?: T;
              link?: T;
              id?: T;
            };
      };
  promotions?:
    | T
    | {
        promotions?: T;
      };
  meta?:
    | T
    | {
        meta?:
          | T
          | {
              title?: T;
              image?: T;
              description?: T;
            };
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "fanNotificationListners_select".
 */
export interface FanNotificationListnersSelect<T extends boolean = true> {
  type?: T;
  event?: T;
  fan?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "fanUsers_select".
 */
export interface FanUsersSelect<T extends boolean = true> {
  firstName?: T;
  lastName?: T;
  email?: T;
  phoneNumber?: T;
  phoneNumberCountryCode?: T;
  dateOfBirth?: T;
  ethWallet?: T;
  followingsGroup?:
    | T
    | {
        eventBrandsFollowings?: T;
        festivalsFollowings?: T;
        artistsFollowings?: T;
        authorsFollowings?: T;
        genresFollowings?: T;
      };
  orders?: T;
  diceId?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "festivalProfiles_select".
 */
export interface FestivalProfilesSelect<T extends boolean = true> {
  syncLock?: T;
  name?: T;
  festivalEditions?: T;
  previewImage?: T;
  socialLinks?:
    | T
    | {
        resource?: T;
        link?: T;
        id?: T;
      };
  externalSanityId?: T;
  origin?: T;
  overview?:
    | T
    | {
        overview?:
          | T
          | {
              hero?: T;
              content?: T;
            };
      };
  meta?:
    | T
    | {
        meta?:
          | T
          | {
              title?: T;
              image?: T;
              description?: T;
            };
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "festivals_select".
 */
export interface FestivalsSelect<T extends boolean = true> {
  syncLock?: T;
  name?: T;
  festivalBrand?: T;
  festivalEditionEvents?: T;
  previewImage?: T;
  socialLinks?:
    | T
    | {
        resource?: T;
        link?: T;
        id?: T;
      };
  externalSanityId?: T;
  origin?: T;
  overview?:
    | T
    | {
        overview?:
          | T
          | {
              hero?: T;
              content?: T;
            };
      };
  meta?:
    | T
    | {
        meta?:
          | T
          | {
              title?: T;
              image?: T;
              description?: T;
            };
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "genres_select".
 */
export interface GenresSelect<T extends boolean = true> {
  name?: T;
  type?: T;
  description?: T;
  overview?:
    | T
    | {
        overview?:
          | T
          | {
              hero?: T;
              content?: T;
            };
      };
  meta?:
    | T
    | {
        meta?:
          | T
          | {
              title?: T;
              image?: T;
              description?: T;
            };
      };
  fullTitle?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "hubs_select".
 */
export interface HubsSelect<T extends boolean = true> {
  name?: T;
  formatted?: T;
  type?: T;
  parent?: T;
  childHubs?: T;
  venues?: T;
  childHubVenues?: T;
  overview?:
    | T
    | {
        overview?:
          | T
          | {
              hero?: T;
              content?: T;
            };
      };
  meta?:
    | T
    | {
        meta?:
          | T
          | {
              title?: T;
              image?: T;
              description?: T;
            };
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "managers_select".
 */
export interface ManagersSelect<T extends boolean = true> {
  name?: T;
  title?: T;
  generalContacInfo?:
    | T
    | {
        email?: T;
        phoneNumber?: T;
      };
  managmentCompany?: T;
  automatedReportingEnabled?: T;
  artists?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "managmentCompanies_select".
 */
export interface ManagmentCompaniesSelect<T extends boolean = true> {
  name?: T;
  address?: T;
  generalContacInfo?:
    | T
    | {
        email?: T;
        phoneNumber?: T;
      };
  managers?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  caption?: T;
  prefix?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        square?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        small?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        medium?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        large?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        xlarge?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        og?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ochoEpisodes_select".
 */
export interface OchoEpisodesSelect<T extends boolean = true> {
  title?: T;
  episodeNumber?: T;
  description?: T;
  videoUrl?: T;
  podcastUrl?: T;
  guests?: T;
  previewImage?: T;
  overview?:
    | T
    | {
        overview?:
          | T
          | {
              hero?: T;
              content?: T;
            };
      };
  meta?:
    | T
    | {
        meta?:
          | T
          | {
              title?: T;
              image?: T;
              description?: T;
            };
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "orders_select".
 */
export interface OrdersSelect<T extends boolean = true> {
  externalDiceId?: T;
  origin?: T;
  externalResidentAdvisorId?: T;
  externalEventBriteId?: T;
  event?: T;
  fanEmail?: T;
  fan?: T;
  tickets?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages_select".
 */
export interface PagesSelect<T extends boolean = true> {
  title?: T;
  layout?:
    | T
    | {
        cta?: T | CallToActionBlockSelect<T>;
        content?: T | ContentBlockSelect<T>;
        mediaBlock?: T | MediaBlockSelect<T>;
        archive?: T | ArchiveBlockSelect<T>;
        eventsBlock?: T | EventsBlockSelect<T>;
        homeEventsSection?: T | HomeEventsSectionSelect<T>;
      };
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CallToActionBlock_select".
 */
export interface CallToActionBlockSelect<T extends boolean = true> {
  richText?: T;
  links?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
              appearance?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentBlock_select".
 */
export interface ContentBlockSelect<T extends boolean = true> {
  columns?:
    | T
    | {
        size?: T;
        richText?: T;
        enableLink?: T;
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
              appearance?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MediaBlock_select".
 */
export interface MediaBlockSelect<T extends boolean = true> {
  media?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ArchiveBlock_select".
 */
export interface ArchiveBlockSelect<T extends boolean = true> {
  introContent?: T;
  populateBy?: T;
  relationTo?: T;
  limit?: T;
  selectedDocs?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "eventsBlock_select".
 */
export interface EventsBlockSelect<T extends boolean = true> {
  heading?: T;
  events?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "homeEventsSection_select".
 */
export interface HomeEventsSectionSelect<T extends boolean = true> {
  title?: T;
  events?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "promotions_select".
 */
export interface PromotionsSelect<T extends boolean = true> {
  title?: T;
  startDate?: T;
  endDate?: T;
  maxRedemptions?: T;
  codeLocks?:
    | T
    | {
        code?: T;
        claimedBy?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "residencies_select".
 */
export interface ResidenciesSelect<T extends boolean = true> {
  syncLock?: T;
  name?: T;
  eventBrand?: T;
  events?: T;
  Location?:
    | T
    | {
        venue?: T;
        geoLocation?:
          | T
          | {
              locationName?: T;
              link?: T;
            };
      };
  previewImage?: T;
  socialLinks?:
    | T
    | {
        resource?: T;
        link?: T;
        id?: T;
      };
  overview?:
    | T
    | {
        overview?:
          | T
          | {
              hero?: T;
              content?: T;
            };
      };
  meta?:
    | T
    | {
        meta?:
          | T
          | {
              title?: T;
              image?: T;
              description?: T;
            };
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tickets_select".
 */
export interface TicketsSelect<T extends boolean = true> {
  externalDiceId?: T;
  admittedDate?: T;
  code?: T;
  total?: T;
  ticketType?: T;
  order?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ticketTypes_select".
 */
export interface TicketTypesSelect<T extends boolean = true> {
  syncLock?: T;
  origin?: T;
  externalDiceId?:
    | T
    | {
        ticketTypeId?: T;
        priceTierId?: T;
      };
  externalResidentAdvisorId?: T;
  externalEventBriteId?: T;
  ticketTypeName?: T;
  label?: T;
  description?: T;
  archived?: T;
  internalPresentationOnly?: T;
  allocation?: T;
  faceValue?: T;
  fee?:
    | T
    | {
        applicable?: T;
        amount?: T;
        computed?: T;
        type?: T;
        unit?: T;
        id?: T;
      };
  totalFee?: T;
  total?: T;
  tickets?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  name?: T;
  roles?: T;
  tenants?:
    | T
    | {
        tenant?: T;
        roles?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "venues_select".
 */
export interface VenuesSelect<T extends boolean = true> {
  syncLock?: T;
  name?: T;
  address?: T;
  city?: T;
  country?: T;
  coordinates?: T;
  timezone?: T;
  hub?: T;
  capacities?:
    | T
    | {
        title?: T;
        capacity?: T;
        id?: T;
      };
  previewImage?: T;
  mediaAssets?: T;
  accessibility?: T;
  internalContacts?:
    | T
    | {
        type?: T;
        name?: T;
        phoneNumber?: T;
        emailAddress?: T;
        id?: T;
      };
  houseRules?: T;
  productionTechSpecs?: T;
  invoicingInfo?: T;
  closestAirport?: T;
  socialLinks?:
    | T
    | {
        resource?: T;
        link?: T;
        id?: T;
      };
  origin?: T;
  externalSanityId?: T;
  overview?:
    | T
    | {
        overview?:
          | T
          | {
              hero?: T;
              content?: T;
            };
      };
  meta?:
    | T
    | {
        meta?:
          | T
          | {
              title?: T;
              image?: T;
              description?: T;
            };
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects_select".
 */
export interface RedirectsSelect<T extends boolean = true> {
  from?: T;
  to?:
    | T
    | {
        type?: T;
        reference?: T;
        url?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "search_select".
 */
export interface SearchSelect<T extends boolean = true> {
  title?: T;
  priority?: T;
  doc?: T;
  slug?: T;
  meta?:
    | T
    | {
        title?: T;
        description?: T;
        image?: T;
      };
  categories?:
    | T
    | {
        relationTo?: T;
        id?: T;
        title?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs_select".
 */
export interface PayloadJobsSelect<T extends boolean = true> {
  input?: T;
  taskStatus?: T;
  completedAt?: T;
  totalTried?: T;
  hasError?: T;
  error?: T;
  log?:
    | T
    | {
        executedAt?: T;
        completedAt?: T;
        taskSlug?: T;
        taskID?: T;
        input?: T;
        output?: T;
        state?: T;
        error?: T;
        id?: T;
      };
  workflowSlug?: T;
  taskSlug?: T;
  queue?: T;
  waitUntil?: T;
  processing?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskImportDiceEvent".
 */
export interface TaskImportDiceEvent {
  input: {
    eventIdLive: string;
    event?:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
  };
  output: {
    eventDocId: number;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskExtractEventUrls".
 */
export interface TaskExtractEventUrls {
  input: {
    sitemapUrl: string;
    filterText: string;
  };
  output: {
    eventUrls: string[];
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskGetSitemapUrls".
 */
export interface TaskGetSitemapUrls {
  input: {
    url: string;
  };
  output: {
    sitemapUrls: string[];
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskQflowLogin".
 */
export interface TaskQflowLogin {
  input?: unknown;
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskQflowFetchEvents".
 */
export interface TaskQflowFetchEvents {
  input?: unknown;
  output: {
    events:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskQflowSyncAttendees".
 */
export interface TaskQflowSyncAttendees {
  input: {
    eventId: string;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskFetchEventWithTicketTypes".
 */
export interface TaskFetchEventWithTicketTypes {
  input: {
    token: string;
    eventId: string;
  };
  output: {
    ticketTypes:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskUpsertTicketTypes".
 */
export interface TaskUpsertTicketTypes {
  input: {
    ticketTypes: {}[];
  };
  output: {
    ids: number[];
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskFetchOrders".
 */
export interface TaskFetchOrders {
  input: {
    first: number;
    after?: string | null;
    token: string;
  };
  output: {
    orders:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
    pageInfo:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskUpsertTickets".
 */
export interface TaskUpsertTickets {
  input: {
    tickets: {}[];
  };
  output: {
    ticketIds: number[];
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskGetConcertSitemapUrls".
 */
export interface TaskGetConcertSitemapUrls {
  input: {
    url: string;
  };
  output: {
    sitemapUrls: string[];
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskCollectConcertUrls".
 */
export interface TaskCollectConcertUrls {
  input: {
    sitemapUrls: string[];
  };
  output: {
    urls: string[];
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskFetchHtmlAndSave".
 */
export interface TaskFetchHtmlAndSave {
  input: {
    url: string;
    bucket: string;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskSaveRawSanityData".
 */
export interface TaskSaveRawSanityData {
  input: {
    bucket: string;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskImportSanityData".
 */
export interface TaskImportSanityData {
  input: {
    bucket: string;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskHandleSongkickEvent".
 */
export interface TaskHandleSongkickEvent {
  input: {
    jsonEvent:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
  };
  output: {
    event:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskExtractKeysByBucket".
 */
export interface TaskExtractKeysByBucket {
  input: {
    bucket: string;
  };
  output: {
    keys: string[];
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskExtractLdJson".
 */
export interface TaskExtractLdJson {
  input: {
    html: string;
  };
  output: {
    json:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskExtractRetailerId".
 */
export interface TaskExtractRetailerId {
  input: {
    html: string;
  };
  output: {
    id: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskFindOrCreateEvent".
 */
export interface TaskFindOrCreateEvent {
  input: {
    event:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
  };
  output: {
    eventDocId?: number | null;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskFindOrCreateTicket".
 */
export interface TaskFindOrCreateTicket {
  input: {
    ticket:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
    ticketTypeId: number;
  };
  output: {
    ticketDocId: number;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskCreateOrder".
 */
export interface TaskCreateOrder {
  input: {
    externalDiceId: string;
    eventDocId: number;
    fanEmail?: string | null;
    ticketDocIds: number[];
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskImportPlanetscaleUser".
 */
export interface TaskImportPlanetscaleUser {
  input: {
    row:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskImportPlanetscalePresaleRegistration".
 */
export interface TaskImportPlanetscalePresaleRegistration {
  input: {
    row:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskSchedulePublish".
 */
export interface TaskSchedulePublish {
  input: {
    type?: ('publish' | 'unpublish') | null;
    locale?: string | null;
    doc?: {
      relationTo: 'pages';
      value: number | Page;
    } | null;
    global?: string | null;
    user?: (number | null) | User;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "WorkflowCrawlSanity".
 */
export interface WorkflowCrawlSanity {
  input?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "WorkflowCrawlRAWorkflow".
 */
export interface WorkflowCrawlRAWorkflow {
  input?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "WorkflowCrawlSongkickWorkflow".
 */
export interface WorkflowCrawlSongkickWorkflow {
  input?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "WorkflowCrawlDicekWorkflow".
 */
export interface WorkflowCrawlDicekWorkflow {
  input?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "WorkflowImportSanityDataWorkflow".
 */
export interface WorkflowImportSanityDataWorkflow {
  input?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "WorkflowSyncDicePartnerEventsWorkflow".
 */
export interface WorkflowSyncDicePartnerEventsWorkflow {
  input?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "WorkflowSyncDiceOrdersWorkflow".
 */
export interface WorkflowSyncDiceOrdersWorkflow {
  input?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "WorkflowSyncDiceWorkflow".
 */
export interface WorkflowSyncDiceWorkflow {
  input?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "WorkflowSyncQFlowWorkflow".
 */
export interface WorkflowSyncQFlowWorkflow {
  input?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "WorkflowSyncSongkickWorkflow".
 */
export interface WorkflowSyncSongkickWorkflow {
  input?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "WorkflowSyncPlanetscaleUsersWorkflow".
 */
export interface WorkflowSyncPlanetscaleUsersWorkflow {
  input?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "WorkflowSyncPlanetscalePresaleRegistrationsWorkflow".
 */
export interface WorkflowSyncPlanetscalePresaleRegistrationsWorkflow {
  input?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}