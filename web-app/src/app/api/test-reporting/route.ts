import { NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { SalesService } from '@/lib/reporting/sales.service'

export async function GET() {
  try {
    const payload = await getPayload({ config })
    const salesService = new SalesService(payload)

    const recipients = await salesService.getRecipientsWithReporting()
    
    const recipientsWithEvents = []
    for (const recipient of recipients) {
      const events = await salesService.getEventsForRecipient(
        recipient.recipientId, 
        recipient.recipientType
      )
      recipientsWithEvents.push({
        ...recipient,
        events,
        eventsCount: events.length
      })
    }

    return NextResponse.json({
      success: true,
      recipientsCount: recipients.length,
      recipients: recipientsWithEvents
    })
  } catch (error) {
    console.error('Reporting test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
