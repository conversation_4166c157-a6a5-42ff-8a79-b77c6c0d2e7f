import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { ReportScheduler } from '@/lib/scheduling/reportScheduler'

export async function POST(req: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const scheduler = new ReportScheduler(payload)
    
    const body = await req.json().catch(() => ({}))
    const { type = 'weekly', recipientId, recipientType } = body

    let result

    if (recipientId && recipientType) {
      // Schedule for specific recipient
      result = await scheduler.scheduleReportForRecipient(
        recipientId,
        recipientType,
        type
      )
    } else if (type === 'weekly') {
      // Schedule weekly reports for all
      result = await scheduler.scheduleWeeklyReports()
    } else if (type === 'daily') {
      // Schedule daily reports for all
      result = await scheduler.scheduleDailyReports()
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid report type. Use "weekly" or "daily"'
      }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      message: 'Report job scheduled successfully',
      jobId: result.id,
      type,
      recipientId,
      recipientType,
    })
  } catch (error) {
    console.error('Schedule reports error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET(req: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const scheduler = new ReportScheduler(payload)
    
    const { searchParams } = new URL(req.url)
    const queue = searchParams.get('queue') || 'default'

    const status = await scheduler.getQueueStatus(queue)

    return NextResponse.json({
      success: true,
      ...status,
    })
  } catch (error) {
    console.error('Get queue status error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
