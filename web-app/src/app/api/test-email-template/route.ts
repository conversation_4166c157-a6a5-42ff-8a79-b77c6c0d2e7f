import { NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { ReportGenerator } from '@/lib/reporting/report-generator'

export async function POST() {
  try {
    const payload = await getPayload({ config })
    
    // Mock test data
    const mockRecipient = {
      recipientId: 1,
      recipientName: 'Test Agent',
      recipientEmail: '<EMAIL>',
      recipientType: 'agent' as const,
      events: [
        {
          eventId: 1,
          eventName: 'Test Concert at Gray Area',
          eventDate: 'Friday, December 20, 2024',
          artistName: 'Test Artist',
          venueName: 'Gray Area Theater',
          ticketSales: [
            {
              ticketTypeName: 'General Admission',
              ticketsSold: 45,
              faceValue: 5000, // $50.00 in cents
              totalGrossValue: 225000, // $2,250.00
            },
            {
              ticketTypeName: 'VIP',
              ticketsSold: 12,
              faceValue: 10000, // $100.00 in cents
              totalGrossValue: 120000, // $1,200.00
            },
            {
              ticketTypeName: 'Early Bird',
              ticketsSold: 28,
              faceValue: 3500, // $35.00 in cents
              totalGrossValue: 98000, // $980.00
            }
          ],
          totalTicketsSold: 85,
          totalGrossRevenue: 443000, // $4,430.00
        },
        {
          eventId: 2,
          eventName: 'Electronic Music Night',
          eventDate: 'Saturday, December 21, 2024',
          artistName: 'Test Artist',
          venueName: 'Gray Area Main Stage',
          ticketSales: [
            {
              ticketTypeName: 'General Admission',
              ticketsSold: 67,
              faceValue: 4500, // $45.00 in cents
              totalGrossValue: 301500, // $3,015.00
            },
            {
              ticketTypeName: 'Student Discount',
              ticketsSold: 15,
              faceValue: 2500, // $25.00 in cents
              totalGrossValue: 37500, // $375.00
            }
          ],
          totalTicketsSold: 82,
          totalGrossRevenue: 339000, // $3,390.00
        }
      ]
    }

    // Generate HTML using your template
    const reportGenerator = new ReportGenerator(payload)
    
    // Use reflection to access private method for testing
    const generateHtml = (reportGenerator as any).generateHtml.bind(reportGenerator)
    const html = await generateHtml(mockRecipient)

    if (!html) {
      return NextResponse.json({ 
        success: false, 
        error: 'No HTML generated' 
      }, { status: 400 })
    }

    // Send email
    await payload.sendEmail({
      to: '<EMAIL>',
      subject: `Gray Area Sales Report for ${mockRecipient.recipientName}`,
      html,
    })

    return NextResponse.json({
      success: true,
      message: 'Test email with template sent successfully',
      recipientName: mockRecipient.recipientName,
      eventsCount: mockRecipient.events.length,
      totalRevenue: mockRecipient.events.reduce((sum, event) => sum + event.totalGrossRevenue, 0)
    })
  } catch (error) {
    console.error('Template test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
